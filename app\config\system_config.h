#ifndef _SYSTEM_CONFIG_H
#define _SYSTEM_CONFIG_H

#include "stdint.h"
#include "stdbool.h"

// 配置版本号
#define CONFIG_VERSION          0x01

// 传感器配置
#define SENSOR_COUNT            8
#define SENSOR_FILTER_SIZE      8

// 配置存储地址(Flash地址，需要根据实际芯片调整)
#define CONFIG_FLASH_ADDR       0x08007000  // 示例地址

/**
 * @brief 传感器配置结构体
 */
typedef struct {
    uint16_t black_values[SENSOR_COUNT];    // 黑线校准值
    uint16_t white_values[SENSOR_COUNT];    // 白线校准值
    uint16_t threshold_values[SENSOR_COUNT]; // 阈值(可选)
    uint8_t filter_enable;                  // 滤波使能
    uint8_t direction;                      // 传感器方向
} sensor_config_t;

/**
 * @brief PID控制器配置结构体
 */
typedef struct {
    // 电机A PID参数
    float motor_a_kp;
    float motor_a_ki;
    float motor_a_kd;
    
    // 电机B PID参数
    float motor_b_kp;
    float motor_b_ki;
    float motor_b_kd;
    
    // 输出限制
    float output_max;
    float output_min;
    
    // 积分限幅
    float integral_max;
    float integral_min;
    
    // 采样周期
    float sample_time;
} pid_config_t;

/**
 * @brief 电机控制配置结构体
 */
typedef struct {
    int16_t base_speed;         // 基础速度
    int16_t turn_speed_diff;    // 转弯速度差
    int16_t large_turn_speed;   // 大转弯速度
    int16_t max_speed;          // 最大速度
    int16_t min_speed;          // 最小速度
    uint8_t motor_direction_a;  // 电机A方向
    uint8_t motor_direction_b;  // 电机B方向
} motor_config_t;

/**
 * @brief 路径控制配置结构体
 */
typedef struct {
    uint16_t lost_line_threshold;   // 丢线判断阈值
    uint16_t search_timeout;        // 搜索超时时间
    uint8_t search_direction;       // 搜索方向(0:右转, 1:左转)
    uint8_t auto_calibration;       // 自动校准使能
} path_config_t;

/**
 * @brief 显示配置结构体
 */
typedef struct {
    uint8_t oled_enable;            // OLED显示使能
    uint8_t refresh_rate;           // 刷新率(Hz)
    uint8_t brightness;             // 亮度(0-255)
    uint8_t display_mode;           // 显示模式
} display_config_t;

/**
 * @brief 调试配置结构体
 */
typedef struct {
    uint8_t uart_enable;            // 串口调试使能
    uint32_t uart_baudrate;         // 波特率
    uint8_t log_level;              // 日志级别
    uint8_t debug_mode;             // 调试模式
} debug_config_t;

/**
 * @brief 系统总配置结构体
 */
typedef struct {
    uint32_t magic;                 // 魔数，用于验证配置有效性
    uint16_t version;               // 配置版本
    uint16_t checksum;              // 校验和
    
    sensor_config_t sensor;         // 传感器配置
    pid_config_t pid;               // PID配置
    motor_config_t motor;           // 电机配置
    path_config_t path;             // 路径配置
    display_config_t display;       // 显示配置
    debug_config_t debug;           // 调试配置
    
    uint32_t reserved[8];           // 保留字段，用于扩展
} system_config_t;

// 配置魔数
#define CONFIG_MAGIC            0x12345678

// 默认配置值
#define DEFAULT_BASE_SPEED      80
#define DEFAULT_TURN_DIFF       40
#define DEFAULT_LARGE_TURN      120
#define DEFAULT_PID_KP          50.0f
#define DEFAULT_PID_KI          0.5f
#define DEFAULT_PID_KD          2.0f

/**
 * @brief 系统配置初始化
 * @return true 成功, false 失败
 */
bool system_config_init(void);

/**
 * @brief 加载配置(从Flash)
 * @return true 成功, false 失败
 */
bool config_load_from_flash(void);

/**
 * @brief 保存配置(到Flash)
 * @return true 成功, false 失败
 */
bool config_save_to_flash(void);

/**
 * @brief 恢复默认配置
 */
void config_restore_defaults(void);

/**
 * @brief 获取系统配置指针
 * @return system_config_t* 配置结构体指针
 */
system_config_t* get_system_config(void);

/**
 * @brief 验证配置有效性
 * @param config 配置结构体指针
 * @return true 有效, false 无效
 */
bool config_validate(const system_config_t* config);

/**
 * @brief 计算配置校验和
 * @param config 配置结构体指针
 * @return uint16_t 校验和
 */
uint16_t config_calculate_checksum(const system_config_t* config);

/**
 * @brief 获取传感器配置
 * @return sensor_config_t* 传感器配置指针
 */
sensor_config_t* get_sensor_config(void);

/**
 * @brief 获取PID配置
 * @return pid_config_t* PID配置指针
 */
pid_config_t* get_pid_config(void);

/**
 * @brief 获取电机配置
 * @return motor_config_t* 电机配置指针
 */
motor_config_t* get_motor_config(void);

/**
 * @brief 获取路径配置
 * @return path_config_t* 路径配置指针
 */
path_config_t* get_path_config(void);

/**
 * @brief 更新传感器校准值
 * @param black_values 黑线校准值数组
 * @param white_values 白线校准值数组
 */
void config_update_sensor_calibration(const uint16_t* black_values, const uint16_t* white_values);

/**
 * @brief 更新PID参数
 * @param motor_id 电机ID (0:A, 1:B)
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void config_update_pid_params(uint8_t motor_id, float kp, float ki, float kd);

/**
 * @brief 更新电机参数
 * @param base_speed 基础速度
 * @param turn_diff 转弯速度差
 * @param large_turn 大转弯速度
 */
void config_update_motor_params(int16_t base_speed, int16_t turn_diff, int16_t large_turn);

#endif // _SYSTEM_CONFIG_H
