******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 16:09:02 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002c11


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004ea0  0001b160  R  X
  SRAM                  20200000   00008000  00000933  000076cd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004ea0   00004ea0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003338   00003338    r-x .text
  000033f8    000033f8    00001a50   00001a50    r-- .rodata
  00004e48    00004e48    00000058   00000058    r-- .cinit
20200000    20200000    00000736   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    000001d6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003338     
                  000000c0    000005d2     Ganway.o (.text.Way)
                  00000692    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000694    000001d0     oled.o (.text.OLED_ShowChar)
                  00000864    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b8a    00000002     --HOLE-- [fill = 0]
                  00000b8c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000d14    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000e70    00000128     empty.o (.text.main)
                  00000f98    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  000010b8    0000010c     motor.o (.text.Set_PWM)
                  000011c4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000012d0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000013d4    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000014d4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000015bc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000016a0    000000e2     oled.o (.text.OLED_ShowNum)
                  00001782    000000de     oled.o (.text.OLED_Init)
                  00001860    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000193c    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001a0c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001ab6    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001b50    0000009a     oled.o (.text.OLED_ShowString)
                  00001bea    00000002     --HOLE-- [fill = 0]
                  00001bec    00000090     oled.o (.text.OLED_DrawPoint)
                  00001c7c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001d08    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001d94    00000084     oled.o (.text.OLED_Refresh)
                  00001e18    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001e9c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001f18    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001f8c    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001ffe    00000002     --HOLE-- [fill = 0]
                  00002000    0000006c     oled.o (.text.OLED_WR_Byte)
                  0000206c    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000020d8    00000068     key.o (.text.Key_1)
                  00002140    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000021a8    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000220a    00000002     --HOLE-- [fill = 0]
                  0000220c    00000060     oled.o (.text.OLED_Clear)
                  0000226c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000022ca    00000002     --HOLE-- [fill = 0]
                  000022cc    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002324    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002378    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000023c8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002418    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002464    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000024b0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000024fa    00000002     --HOLE-- [fill = 0]
                  000024fc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002546    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002590    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000025d8    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002620    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002668    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000026b0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000026f4    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002736    00000002     --HOLE-- [fill = 0]
                  00002738    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002778    00000040     key.o (.text.Key)
                  000027b8    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  000027f8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002838    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002874    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000028b0    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000028ec    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002928    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002962    00000002     --HOLE-- [fill = 0]
                  00002964    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002998    00000034     oled.o (.text.OLED_ColorTurn)
                  000029cc    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002a00    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002a34    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00002a64    00000030     oled.o (.text.OLED_Pow)
                  00002a94    00000030     systick.o (.text.SysTick_Handler)
                  00002ac4    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002af0    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002b1c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002b48    00000028     empty.o (.text.DL_Common_updateReg)
                  00002b70    00000028     oled.o (.text.DL_Common_updateReg)
                  00002b98    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002bc0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002be8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002c10    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002c38    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002c5e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002c84    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002ca8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002cc8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002ce8    00000020     systick.o (.text.delay_ms)
                  00002d08    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002d26    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002d44    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002d60    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002d7c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002d98    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002db4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002dd0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002dec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002e08    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002e24    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002e40    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002e5c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002e78    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002e94    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002eb0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002ec8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002ee0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002ef8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002f10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002f28    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002f40    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002f58    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002f70    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002f88    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002fa0    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002fb8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002fd0    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00002fe8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003000    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003018    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003030    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003048    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003060    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003078    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003090    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000030a8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000030c0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000030d8    00000018     empty.o (.text.DL_Timer_startCounter)
                  000030f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003108    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003120    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00003136    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  0000314c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003162    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003178    00000016     key.o (.text.DL_GPIO_readPins)
                  0000318e    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000031a4    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000031b8    00000014     empty.o (.text.DL_GPIO_clearPins)
                  000031cc    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000031e0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000031f4    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003208    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000321c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003230    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003244    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003258    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  0000326c    00000014     key.o (.text.Key_Init_Debounce)
                  00003280    00000014     motor.o (.text.Left_Control)
                  00003294    00000014     motor.o (.text.Left_Little_Control)
                  000032a8    00000014     motor.o (.text.Right_Control)
                  000032bc    00000014     motor.o (.text.Right_Little_Control)
                  000032d0    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000032e2    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000032f4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003306    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003318    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000332a    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  0000333a    00000002     --HOLE-- [fill = 0]
                  0000333c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000334c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000335c    00000010     key.o (.text.Key_System_Tick_Inc)
                  0000336c    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  0000337c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000338a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003398    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000033a4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000033b0    0000000c     systick.o (.text.get_systicks)
                  000033bc    0000000c     Scheduler.o (.text.scheduler_init)
                  000033c8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000033d2    00000002     --HOLE-- [fill = 0]
                  000033d4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000033dc    00000006     libc.a : exit.c.obj (.text:abort)
                  000033e2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000033e6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000033ea    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000033ee    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000033f2    00000006     --HOLE-- [fill = 0]

.cinit     0    00004e48    00000058     
                  00004e48    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004e77    00000001     --HOLE-- [fill = 0]
                  00004e78    0000000c     (__TI_handler_table)
                  00004e84    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004e8c    00000010     (__TI_cinit_table)
                  00004e9c    00000004     --HOLE-- [fill = 0]

.rodata    0    000033f8    00001a50     
                  000033f8    00000d5c     oled.o (.rodata.asc2_2412)
                  00004154    000005f0     oled.o (.rodata.asc2_1608)
                  00004744    00000474     oled.o (.rodata.asc2_1206)
                  00004bb8    00000228     oled.o (.rodata.asc2_0806)
                  00004de0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004e08    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004e1c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004e26    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004e28    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004e30    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004e38    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004e3b    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004e3e    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004e41    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004e43    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    000001d6     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000010     empty.o (.data.Anolog)
                  202006f0    00000010     empty.o (.data.black)
                  20200700    00000010     empty.o (.data.white)
                  20200710    0000000c     key.o (.data.key1_ctrl)
                  2020071c    00000004     empty.o (.data.D_Num)
                  20200720    00000008     systick.o (.data.systicks)
                  20200728    00000004     empty.o (.data.Run)
                  2020072c    00000004     systick.o (.data.delay_times)
                  20200730    00000004     key.o (.data.system_tick_ms)
                  20200734    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200735    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          866     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3646    291       516    
                                                                 
    .\app\
       Ganway.o                         1490    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       motor.o                          392     0         0      
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4244    0         33     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     13086   7006      2355   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004e8c records: 2, size/record: 8, table size: 16
	.data: load addr=00004e48, load size=0000002f bytes, run addr=20200560, run size=000001d6 bytes, compression=lzss
	.bss: load addr=00004e84, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004e78 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000033e3  ADC0_IRQHandler                      
000033e3  ADC1_IRQHandler                      
000033e3  AES_IRQHandler                       
202006e0  Anolog                               
000033e6  C$$EXIT                              
000033e3  CANFD0_IRQHandler                    
000033e3  DAC0_IRQHandler                      
00002739  DL_ADC12_setClockConfig              
000033c9  DL_Common_delayCycles                
0000226d  DL_I2C_fillControllerTXFIFO          
00002c5f  DL_I2C_setClockConfig                
00001861  DL_SYSCTL_configSYSPLL               
000026b1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000012d1  DL_Timer_initFourCCPWMMode           
000014d5  DL_Timer_initTimerMode               
00002e5d  DL_Timer_setCaptCompUpdateMethod     
000030c1  DL_Timer_setCaptureCompareOutCtl     
0000334d  DL_Timer_setCaptureCompareValue      
00002e79  DL_Timer_setClockConfig              
00002591  DL_UART_init                         
000032f5  DL_UART_setClockConfig               
000033e3  DMA_IRQHandler                       
2020071c  D_Num                                
000033e3  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000033e3  GROUP0_IRQHandler                    
00000f99  GROUP1_IRQHandler                    
0000193d  Get_Analog_value                     
000028b1  Get_Anolog_Value                     
0000337d  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000033e7  HOSTexit                             
000033e3  HardFault_Handler                    
000033e3  I2C0_IRQHandler                      
000033e3  I2C1_IRQHandler                      
00002779  Key                                  
000020d9  Key_1                                
0000326d  Key_Init_Debounce                    
00000d15  Key_Scan_Debounce                    
0000335d  Key_System_Tick_Inc                  
00003281  Left_Control                         
00003295  Left_Little_Control                  
000033e3  NMI_Handler                          
00000b8d  No_MCU_Ganv_Sensor_Init              
00001f8d  No_MCU_Ganv_Sensor_Init_Frist        
000026f5  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000220d  OLED_Clear                           
00002999  OLED_ColorTurn                       
000025d9  OLED_DisplayTurn                     
00001bed  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001783  OLED_Init                            
00002a65  OLED_Pow                             
00001d95  OLED_Refresh                         
00000695  OLED_ShowChar                        
000016a1  OLED_ShowNum                         
00001ab7  OLED_ShowSignedNum                   
00001b51  OLED_ShowString                      
00002001  OLED_WR_Byte                         
000033e3  PendSV_Handler                       
000033e3  RTC_IRQHandler                       
000033eb  Reset_Handler                        
000032a9  Right_Control                        
000032bd  Right_Little_Control                 
20200728  Run                                  
000033e3  SPI0_IRQHandler                      
000033e3  SPI1_IRQHandler                      
000033e3  SVC_Handler                          
00002621  SYSCFG_DL_ADC12_0_init               
00000865  SYSCFG_DL_GPIO_init                  
000022cd  SYSCFG_DL_I2C_OLED_init              
00001c7d  SYSCFG_DL_PWM_0_init                 
00002669  SYSCFG_DL_SYSCTL_init                
00003399  SYSCFG_DL_SYSTICK_init               
000029cd  SYSCFG_DL_TIMER_0_init               
00002325  SYSCFG_DL_UART_0_init                
00002a01  SYSCFG_DL_init                       
00001d09  SYSCFG_DL_initPower                  
000010b9  Set_PWM                              
00002a95  SysTick_Handler                      
000033e3  TIMA0_IRQHandler                     
000033e3  TIMA1_IRQHandler                     
000013d5  TIMG0_IRQHandler                     
000033e3  TIMG12_IRQHandler                    
000033e3  TIMG6_IRQHandler                     
000033e3  TIMG7_IRQHandler                     
000033e3  TIMG8_IRQHandler                     
00003307  TI_memcpy_small                      
0000338b  TI_memset_small                      
000027b9  UART0_IRQHandler                     
000033e3  UART1_IRQHandler                     
000033e3  UART2_IRQHandler                     
000033e3  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004e8c  __TI_CINIT_Base                      
00004e9c  __TI_CINIT_Limit                     
00004e9c  __TI_CINIT_Warm                      
00004e78  __TI_Handler_Table_Base              
00004e84  __TI_Handler_Table_Limit             
000028ed  __TI_auto_init_nobinit_nopinit       
00001e9d  __TI_decompress_lzss                 
00003319  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000336d  __TI_zero_init                       
00000a03  __adddf3                             
000024fd  __aeabi_d2iz                         
00000a03  __aeabi_dadd                         
000021a9  __aeabi_dcmpeq                       
000021e5  __aeabi_dcmpge                       
000021f9  __aeabi_dcmpgt                       
000021d1  __aeabi_dcmple                       
000021bd  __aeabi_dcmplt                       
000011c5  __aeabi_ddiv                         
000015bd  __aeabi_dmul                         
000009f9  __aeabi_dsub                         
00002b1d  __aeabi_i2d                          
00000693  __aeabi_idiv0                        
000033a5  __aeabi_memclr                       
000033a5  __aeabi_memclr4                      
000033a5  __aeabi_memclr8                      
000033d5  __aeabi_memcpy                       
000033d5  __aeabi_memcpy4                      
000033d5  __aeabi_memcpy8                      
00002c85  __aeabi_ui2d                         
000027f9  __aeabi_uidiv                        
000027f9  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002141  __cmpdf2                             
000011c5  __divdf3                             
00002141  __eqdf2                              
000024fd  __fixdfsi                            
00002b1d  __floatsidf                          
00002c85  __floatunsidf                        
00001f19  __gedf2                              
00001f19  __gtdf2                              
00002141  __ledf2                              
00002141  __ltdf2                              
UNDEFED   __mpu_init                           
000015bd  __muldf3                             
00002929  __muldsi3                            
00002141  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009f9  __subdf3                             
00002c11  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000033ef  _system_pre_init                     
000033dd  abort                                
00002547  adc_getValue                         
00004bb8  asc2_0806                            
00004744  asc2_1206                            
00004154  asc2_1608                            
000033f8  asc2_2412                            
ffffffff  binit                                
202006f0  black                                
0000206d  convertAnalogToDigital               
00002ce9  delay_ms                             
2020072c  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
000033b1  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
20200710  key1_ctrl                            
00000e71  main                                 
00001a0d  normalizeAnalogValues                
20200560  rx_buff                              
000033bd  scheduler_init                       
2020055c  task_num                             
20200660  uart_rx_buffer                       
20200734  uart_rx_index                        
20200735  uart_rx_ticks                        
20200700  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000693  __aeabi_idiv0                        
00000695  OLED_ShowChar                        
00000865  SYSCFG_DL_GPIO_init                  
000009f9  __aeabi_dsub                         
000009f9  __subdf3                             
00000a03  __adddf3                             
00000a03  __aeabi_dadd                         
00000b8d  No_MCU_Ganv_Sensor_Init              
00000d15  Key_Scan_Debounce                    
00000e71  main                                 
00000f99  GROUP1_IRQHandler                    
000010b9  Set_PWM                              
000011c5  __aeabi_ddiv                         
000011c5  __divdf3                             
000012d1  DL_Timer_initFourCCPWMMode           
000013d5  TIMG0_IRQHandler                     
000014d5  DL_Timer_initTimerMode               
000015bd  __aeabi_dmul                         
000015bd  __muldf3                             
000016a1  OLED_ShowNum                         
00001783  OLED_Init                            
00001861  DL_SYSCTL_configSYSPLL               
0000193d  Get_Analog_value                     
00001a0d  normalizeAnalogValues                
00001ab7  OLED_ShowSignedNum                   
00001b51  OLED_ShowString                      
00001bed  OLED_DrawPoint                       
00001c7d  SYSCFG_DL_PWM_0_init                 
00001d09  SYSCFG_DL_initPower                  
00001d95  OLED_Refresh                         
00001e9d  __TI_decompress_lzss                 
00001f19  __gedf2                              
00001f19  __gtdf2                              
00001f8d  No_MCU_Ganv_Sensor_Init_Frist        
00002001  OLED_WR_Byte                         
0000206d  convertAnalogToDigital               
000020d9  Key_1                                
00002141  __cmpdf2                             
00002141  __eqdf2                              
00002141  __ledf2                              
00002141  __ltdf2                              
00002141  __nedf2                              
000021a9  __aeabi_dcmpeq                       
000021bd  __aeabi_dcmplt                       
000021d1  __aeabi_dcmple                       
000021e5  __aeabi_dcmpge                       
000021f9  __aeabi_dcmpgt                       
0000220d  OLED_Clear                           
0000226d  DL_I2C_fillControllerTXFIFO          
000022cd  SYSCFG_DL_I2C_OLED_init              
00002325  SYSCFG_DL_UART_0_init                
000024fd  __aeabi_d2iz                         
000024fd  __fixdfsi                            
00002547  adc_getValue                         
00002591  DL_UART_init                         
000025d9  OLED_DisplayTurn                     
00002621  SYSCFG_DL_ADC12_0_init               
00002669  SYSCFG_DL_SYSCTL_init                
000026b1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000026f5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002739  DL_ADC12_setClockConfig              
00002779  Key                                  
000027b9  UART0_IRQHandler                     
000027f9  __aeabi_uidiv                        
000027f9  __aeabi_uidivmod                     
000028b1  Get_Anolog_Value                     
000028ed  __TI_auto_init_nobinit_nopinit       
00002929  __muldsi3                            
00002999  OLED_ColorTurn                       
000029cd  SYSCFG_DL_TIMER_0_init               
00002a01  SYSCFG_DL_init                       
00002a65  OLED_Pow                             
00002a95  SysTick_Handler                      
00002b1d  __aeabi_i2d                          
00002b1d  __floatsidf                          
00002c11  _c_int00_noargs                      
00002c5f  DL_I2C_setClockConfig                
00002c85  __aeabi_ui2d                         
00002c85  __floatunsidf                        
00002ce9  delay_ms                             
00002e5d  DL_Timer_setCaptCompUpdateMethod     
00002e79  DL_Timer_setClockConfig              
000030c1  DL_Timer_setCaptureCompareOutCtl     
0000326d  Key_Init_Debounce                    
00003281  Left_Control                         
00003295  Left_Little_Control                  
000032a9  Right_Control                        
000032bd  Right_Little_Control                 
000032f5  DL_UART_setClockConfig               
00003307  TI_memcpy_small                      
00003319  __TI_decompress_none                 
0000334d  DL_Timer_setCaptureCompareValue      
0000335d  Key_System_Tick_Inc                  
0000336d  __TI_zero_init                       
0000337d  Get_Digtal_For_User                  
0000338b  TI_memset_small                      
00003399  SYSCFG_DL_SYSTICK_init               
000033a5  __aeabi_memclr                       
000033a5  __aeabi_memclr4                      
000033a5  __aeabi_memclr8                      
000033b1  get_systicks                         
000033bd  scheduler_init                       
000033c9  DL_Common_delayCycles                
000033d5  __aeabi_memcpy                       
000033d5  __aeabi_memcpy4                      
000033d5  __aeabi_memcpy8                      
000033dd  abort                                
000033e3  ADC0_IRQHandler                      
000033e3  ADC1_IRQHandler                      
000033e3  AES_IRQHandler                       
000033e3  CANFD0_IRQHandler                    
000033e3  DAC0_IRQHandler                      
000033e3  DMA_IRQHandler                       
000033e3  Default_Handler                      
000033e3  GROUP0_IRQHandler                    
000033e3  HardFault_Handler                    
000033e3  I2C0_IRQHandler                      
000033e3  I2C1_IRQHandler                      
000033e3  NMI_Handler                          
000033e3  PendSV_Handler                       
000033e3  RTC_IRQHandler                       
000033e3  SPI0_IRQHandler                      
000033e3  SPI1_IRQHandler                      
000033e3  SVC_Handler                          
000033e3  TIMA0_IRQHandler                     
000033e3  TIMA1_IRQHandler                     
000033e3  TIMG12_IRQHandler                    
000033e3  TIMG6_IRQHandler                     
000033e3  TIMG7_IRQHandler                     
000033e3  TIMG8_IRQHandler                     
000033e3  UART1_IRQHandler                     
000033e3  UART2_IRQHandler                     
000033e3  UART3_IRQHandler                     
000033e6  C$$EXIT                              
000033e7  HOSTexit                             
000033eb  Reset_Handler                        
000033ef  _system_pre_init                     
000033f8  asc2_2412                            
00004154  asc2_1608                            
00004744  asc2_1206                            
00004bb8  asc2_0806                            
00004e78  __TI_Handler_Table_Base              
00004e84  __TI_Handler_Table_Limit             
00004e8c  __TI_CINIT_Base                      
00004e9c  __TI_CINIT_Limit                     
00004e9c  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
202006e0  Anolog                               
202006f0  black                                
20200700  white                                
20200710  key1_ctrl                            
2020071c  D_Num                                
20200728  Run                                  
2020072c  delay_times                          
20200734  uart_rx_index                        
20200735  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[194 symbols]
