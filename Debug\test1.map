******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 17:00:48 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000031d9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005438  0001abc8  R  X
  SRAM                  20200000   00008000  00000993  0000766d  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005438   00005438    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000038d0   000038d0    r-x .text
  00003990    00003990    00001a50   00001a50    r-- .rodata
  000053e0    000053e0    00000058   00000058    r-- .cinit
20200000    20200000    00000796   00000000    rw-
  20200000    20200000    000005ad   00000000    rw- .bss
  202005b0    202005b0    000001e6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000038d0     
                  000000c0    000005da     Ganway.o (.text.Way)
                  0000069a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000069c    000001d0     oled.o (.text.OLED_ShowChar)
                  0000086c    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000a00    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b92    00000002     --HOLE-- [fill = 0]
                  00000b94    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000d1c    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000e78    0000012c     empty.o (.text.main)
                  00000fa4    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  000010c4    00000120     empty.o (.text.TIMG0_IRQHandler)
                  000011e4    0000010c     motor.o (.text.Set_PWM)
                  000012f0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000013fc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001500    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000015e8    000000e8     pid_controller.o (.text.pid_calculate)
                  000016d0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000017b4    000000e2     oled.o (.text.OLED_ShowNum)
                  00001896    000000de     oled.o (.text.OLED_Init)
                  00001974    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001a50    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001b28    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001bf8    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001ca2    00000002     --HOLE-- [fill = 0]
                  00001ca4    000000a0     pid_controller.o (.text.motor_pid_task)
                  00001d44    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001dde    0000009a     oled.o (.text.OLED_ShowString)
                  00001e78    00000090     oled.o (.text.OLED_DrawPoint)
                  00001f08    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001f94    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002020    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000020ac    00000084     oled.o (.text.OLED_Refresh)
                  00002130    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000021b4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002236    00000002     --HOLE-- [fill = 0]
                  00002238    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000022b4    00000078     pid_controller.o (.text.motor_pid_init)
                  0000232c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000023a0    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002412    00000002     --HOLE-- [fill = 0]
                  00002414    0000006c     oled.o (.text.OLED_WR_Byte)
                  00002480    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000024ec    00000068     key.o (.text.Key_1)
                  00002554    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000025bc    00000064     pid_controller.o (.text.pid_init)
                  00002620    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002682    00000002     --HOLE-- [fill = 0]
                  00002684    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000026e6    00000002     --HOLE-- [fill = 0]
                  000026e8    00000060     oled.o (.text.OLED_Clear)
                  00002748    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000027a6    00000002     --HOLE-- [fill = 0]
                  000027a8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002800    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002854    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000028a4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000028f4    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002940    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000298c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000029d6    00000002     --HOLE-- [fill = 0]
                  000029d8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002a22    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002a6c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002ab4    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002afc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002b44    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002b8c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002bd0    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002c12    00000002     --HOLE-- [fill = 0]
                  00002c14    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002c54    00000040     key.o (.text.Key)
                  00002c94    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002cd4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002d14    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002d50    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002d8c    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002dc8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002e04    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002e40    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002e7c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002eb6    00000002     --HOLE-- [fill = 0]
                  00002eb8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00002ef2    00000002     --HOLE-- [fill = 0]
                  00002ef4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00002f2c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002f60    00000034     oled.o (.text.OLED_ColorTurn)
                  00002f94    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002fc8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002ffc    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  0000302c    00000030     oled.o (.text.OLED_Pow)
                  0000305c    00000030     systick.o (.text.SysTick_Handler)
                  0000308c    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000030b8    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000030e4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003110    00000028     empty.o (.text.DL_Common_updateReg)
                  00003138    00000028     oled.o (.text.DL_Common_updateReg)
                  00003160    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003188    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000031b0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000031d8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003200    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003226    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000324c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003270    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003290    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000032b0    00000020     systick.o (.text.delay_ms)
                  000032d0    00000020     pid_controller.o (.text.motor_set_target_speed)
                  000032f0    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000330e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000332c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00003348    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00003364    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003380    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000339c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000033b8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000033d4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000033f0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000340c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003428    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003444    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003460    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000347c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003498    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000034b0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000034c8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000034e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000034f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003510    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003528    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003540    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003558    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00003570    00000018     empty.o (.text.DL_GPIO_setPins)
                  00003588    00000018     motor.o (.text.DL_GPIO_setPins)
                  000035a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000035b8    00000018     empty.o (.text.DL_GPIO_togglePins)
                  000035d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000035e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003600    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003618    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003630    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003648    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003660    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003678    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003690    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000036a8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000036c0    00000018     empty.o (.text.DL_Timer_startCounter)
                  000036d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000036f0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003708    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  0000371e    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003734    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000374a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003760    00000016     key.o (.text.DL_GPIO_readPins)
                  00003776    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000378c    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000037a0    00000014     empty.o (.text.DL_GPIO_clearPins)
                  000037b4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000037c8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000037dc    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  000037f0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003804    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003818    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000382c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003840    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003854    00000014     key.o (.text.Key_Init_Debounce)
                  00003868    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  0000387a    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  0000388c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000389e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000038b0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000038c2    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  000038d2    00000002     --HOLE-- [fill = 0]
                  000038d4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000038e4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000038f4    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003904    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003914    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003922    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003930    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000393c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003948    0000000c     systick.o (.text.get_systicks)
                  00003954    0000000c     Scheduler.o (.text.scheduler_init)
                  00003960    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000396a    00000002     --HOLE-- [fill = 0]
                  0000396c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003974    00000006     libc.a : exit.c.obj (.text:abort)
                  0000397a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000397e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003982    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003986    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000398a    00000006     --HOLE-- [fill = 0]

.cinit     0    000053e0    00000058     
                  000053e0    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  0000540f    00000001     --HOLE-- [fill = 0]
                  00005410    0000000c     (__TI_handler_table)
                  0000541c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005424    00000010     (__TI_cinit_table)
                  00005434    00000004     --HOLE-- [fill = 0]

.rodata    0    00003990    00001a50     
                  00003990    00000d5c     oled.o (.rodata.asc2_2412)
                  000046ec    000005f0     oled.o (.rodata.asc2_1608)
                  00004cdc    00000474     oled.o (.rodata.asc2_1206)
                  00005150    00000228     oled.o (.rodata.asc2_0806)
                  00005378    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000053a0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000053b4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000053be    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000053c0    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  000053c8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000053d0    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000053d3    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000053d6    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  000053d9    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000053db    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000005ad     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000028     (.common:motor_a_pid)
                  20200564    00000028     (.common:motor_b_pid)
                  2020058c    00000004     (.common:Flag_stop)
                  20200590    00000004     (.common:Flag_stop1)
                  20200594    00000004     (.common:Get_Encoder_countA)
                  20200598    00000004     (.common:Get_Encoder_countB)
                  2020059c    00000004     (.common:encoderA_cnt)
                  202005a0    00000004     (.common:encoderB_cnt)
                  202005a4    00000004     (.common:gpio_interrup1)
                  202005a8    00000004     (.common:gpio_interrup2)
                  202005ac    00000001     (.common:task_num)

.data      0    202005b0    000001e6     UNINITIALIZED
                  202005b0    00000100     empty.o (.data.rx_buff)
                  202006b0    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  20200730    00000010     empty.o (.data.Anolog)
                  20200740    00000010     empty.o (.data.black)
                  20200750    00000010     empty.o (.data.white)
                  20200760    0000000c     key.o (.data.key1_ctrl)
                  2020076c    00000004     empty.o (.data.D_Num)
                  20200770    00000008     systick.o (.data.systicks)
                  20200778    00000004     empty.o (.data.Run)
                  2020077c    00000004     systick.o (.data.delay_times)
                  20200780    00000004     pid_controller.o (.data.last_encoder_a)
                  20200784    00000004     pid_controller.o (.data.last_encoder_b)
                  20200788    00000004     key.o (.data.system_tick_ms)
                  2020078c    00000004     pid_controller.o (.data.target_speed_a)
                  20200790    00000004     pid_controller.o (.data.target_speed_b)
                  20200794    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200795    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       Ganway.o                         1498    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       motor.o                          312     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4172    0         33     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\app\control\
       pid_controller.o                 644     0         96     
    +--+--------------------------------+-------+---------+---------+
       Total:                           644     0         96     
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2332    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     14512   7006      2451   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005424 records: 2, size/record: 8, table size: 16
	.data: load addr=000053e0, load size=0000002f bytes, run addr=202005b0, run size=000001e6 bytes, compression=lzss
	.bss: load addr=0000541c, load size=00000008 bytes, run addr=20200000, run size=000005ad bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005410 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000397b  ADC0_IRQHandler                      
0000397b  ADC1_IRQHandler                      
0000397b  AES_IRQHandler                       
20200730  Anolog                               
0000397e  C$$EXIT                              
0000397b  CANFD0_IRQHandler                    
0000397b  DAC0_IRQHandler                      
00002c15  DL_ADC12_setClockConfig              
00003961  DL_Common_delayCycles                
00002749  DL_I2C_fillControllerTXFIFO          
00003227  DL_I2C_setClockConfig                
00001975  DL_SYSCTL_configSYSPLL               
00002b8d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000013fd  DL_Timer_initFourCCPWMMode           
00001501  DL_Timer_initTimerMode               
00003445  DL_Timer_setCaptCompUpdateMethod     
000036a9  DL_Timer_setCaptureCompareOutCtl     
000038e5  DL_Timer_setCaptureCompareValue      
00003461  DL_Timer_setClockConfig              
00002a6d  DL_UART_init                         
0000388d  DL_UART_setClockConfig               
0000397b  DMA_IRQHandler                       
2020076c  D_Num                                
0000397b  Default_Handler                      
2020058c  Flag_stop                            
20200590  Flag_stop1                           
0000397b  GROUP0_IRQHandler                    
00000fa5  GROUP1_IRQHandler                    
00001b29  Get_Analog_value                     
00002d8d  Get_Anolog_Value                     
00003915  Get_Digtal_For_User                  
20200594  Get_Encoder_countA                   
20200598  Get_Encoder_countB                   
0000397f  HOSTexit                             
0000397b  HardFault_Handler                    
0000397b  I2C0_IRQHandler                      
0000397b  I2C1_IRQHandler                      
00002c55  Key                                  
000024ed  Key_1                                
00003855  Key_Init_Debounce                    
00000d1d  Key_Scan_Debounce                    
000038f5  Key_System_Tick_Inc                  
0000397b  NMI_Handler                          
00000b95  No_MCU_Ganv_Sensor_Init              
000023a1  No_MCU_Ganv_Sensor_Init_Frist        
00002bd1  No_Mcu_Ganv_Sensor_Task_Without_tick 
000026e9  OLED_Clear                           
00002f61  OLED_ColorTurn                       
00002ab5  OLED_DisplayTurn                     
00001e79  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001897  OLED_Init                            
0000302d  OLED_Pow                             
000020ad  OLED_Refresh                         
0000069d  OLED_ShowChar                        
000017b5  OLED_ShowNum                         
00001d45  OLED_ShowSignedNum                   
00001ddf  OLED_ShowString                      
00002415  OLED_WR_Byte                         
0000397b  PendSV_Handler                       
0000397b  RTC_IRQHandler                       
00003983  Reset_Handler                        
20200778  Run                                  
0000397b  SPI0_IRQHandler                      
0000397b  SPI1_IRQHandler                      
0000397b  SVC_Handler                          
00002afd  SYSCFG_DL_ADC12_0_init               
0000086d  SYSCFG_DL_GPIO_init                  
000027a9  SYSCFG_DL_I2C_OLED_init              
00001f09  SYSCFG_DL_PWM_0_init                 
00002b45  SYSCFG_DL_SYSCTL_init                
00003931  SYSCFG_DL_SYSTICK_init               
00002f95  SYSCFG_DL_TIMER_0_init               
00002801  SYSCFG_DL_UART_0_init                
00002fc9  SYSCFG_DL_init                       
00001f95  SYSCFG_DL_initPower                  
000011e5  Set_PWM                              
0000305d  SysTick_Handler                      
0000397b  TIMA0_IRQHandler                     
0000397b  TIMA1_IRQHandler                     
000010c5  TIMG0_IRQHandler                     
0000397b  TIMG12_IRQHandler                    
0000397b  TIMG6_IRQHandler                     
0000397b  TIMG7_IRQHandler                     
0000397b  TIMG8_IRQHandler                     
0000389f  TI_memcpy_small                      
00003923  TI_memset_small                      
00002c95  UART0_IRQHandler                     
0000397b  UART1_IRQHandler                     
0000397b  UART2_IRQHandler                     
0000397b  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005424  __TI_CINIT_Base                      
00005434  __TI_CINIT_Limit                     
00005434  __TI_CINIT_Warm                      
00005410  __TI_Handler_Table_Base              
0000541c  __TI_Handler_Table_Limit             
00002e41  __TI_auto_init_nobinit_nopinit       
00002239  __TI_decompress_lzss                 
000038b1  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003905  __TI_zero_init                       
00000a0b  __adddf3                             
00001a5b  __addsf3                             
000029d9  __aeabi_d2iz                         
00000a0b  __aeabi_dadd                         
00002621  __aeabi_dcmpeq                       
0000265d  __aeabi_dcmpge                       
00002671  __aeabi_dcmpgt                       
00002649  __aeabi_dcmple                       
00002635  __aeabi_dcmplt                       
000012f1  __aeabi_ddiv                         
000016d1  __aeabi_dmul                         
00000a01  __aeabi_dsub                         
00002ef5  __aeabi_f2iz                         
00001a5b  __aeabi_fadd                         
00002685  __aeabi_fcmpeq                       
000026c1  __aeabi_fcmpge                       
000026d5  __aeabi_fcmpgt                       
000026ad  __aeabi_fcmple                       
00002699  __aeabi_fcmplt                       
000021b5  __aeabi_fdiv                         
00002021  __aeabi_fmul                         
00001a51  __aeabi_fsub                         
000030e5  __aeabi_i2d                          
00002dc9  __aeabi_i2f                          
0000069b  __aeabi_idiv0                        
0000393d  __aeabi_memclr                       
0000393d  __aeabi_memclr4                      
0000393d  __aeabi_memclr8                      
0000396d  __aeabi_memcpy                       
0000396d  __aeabi_memcpy4                      
0000396d  __aeabi_memcpy8                      
0000324d  __aeabi_ui2d                         
00002cd5  __aeabi_uidiv                        
00002cd5  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002555  __cmpdf2                             
00002e7d  __cmpsf2                             
000012f1  __divdf3                             
000021b5  __divsf3                             
00002555  __eqdf2                              
00002e7d  __eqsf2                              
000029d9  __fixdfsi                            
00002ef5  __fixsfsi                            
000030e5  __floatsidf                          
00002dc9  __floatsisf                          
0000324d  __floatunsidf                        
0000232d  __gedf2                              
00002e05  __gesf2                              
0000232d  __gtdf2                              
00002e05  __gtsf2                              
00002555  __ledf2                              
00002e7d  __lesf2                              
00002555  __ltdf2                              
00002e7d  __ltsf2                              
UNDEFED   __mpu_init                           
000016d1  __muldf3                             
00002eb9  __muldsi3                            
00002021  __mulsf3                             
00002555  __nedf2                              
00002e7d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000a01  __subdf3                             
00001a51  __subsf3                             
000031d9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003987  _system_pre_init                     
00003975  abort                                
00002a23  adc_getValue                         
00005150  asc2_0806                            
00004cdc  asc2_1206                            
000046ec  asc2_1608                            
00003990  asc2_2412                            
ffffffff  binit                                
20200740  black                                
00002481  convertAnalogToDigital               
000032b1  delay_ms                             
2020077c  delay_times                          
2020059c  encoderA_cnt                         
202005a0  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003949  get_systicks                         
202005a4  gpio_interrup1                       
202005a8  gpio_interrup2                       
00000000  interruptVectors                     
20200760  key1_ctrl                            
00000e79  main                                 
2020053c  motor_a_pid                          
20200564  motor_b_pid                          
000022b5  motor_pid_init                       
00001ca5  motor_pid_task                       
000032d1  motor_set_target_speed               
00001bf9  normalizeAnalogValues                
000015e9  pid_calculate                        
000025bd  pid_init                             
202005b0  rx_buff                              
00003955  scheduler_init                       
202005ac  task_num                             
202006b0  uart_rx_buffer                       
20200794  uart_rx_index                        
20200795  uart_rx_ticks                        
20200750  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
0000069b  __aeabi_idiv0                        
0000069d  OLED_ShowChar                        
0000086d  SYSCFG_DL_GPIO_init                  
00000a01  __aeabi_dsub                         
00000a01  __subdf3                             
00000a0b  __adddf3                             
00000a0b  __aeabi_dadd                         
00000b95  No_MCU_Ganv_Sensor_Init              
00000d1d  Key_Scan_Debounce                    
00000e79  main                                 
00000fa5  GROUP1_IRQHandler                    
000010c5  TIMG0_IRQHandler                     
000011e5  Set_PWM                              
000012f1  __aeabi_ddiv                         
000012f1  __divdf3                             
000013fd  DL_Timer_initFourCCPWMMode           
00001501  DL_Timer_initTimerMode               
000015e9  pid_calculate                        
000016d1  __aeabi_dmul                         
000016d1  __muldf3                             
000017b5  OLED_ShowNum                         
00001897  OLED_Init                            
00001975  DL_SYSCTL_configSYSPLL               
00001a51  __aeabi_fsub                         
00001a51  __subsf3                             
00001a5b  __addsf3                             
00001a5b  __aeabi_fadd                         
00001b29  Get_Analog_value                     
00001bf9  normalizeAnalogValues                
00001ca5  motor_pid_task                       
00001d45  OLED_ShowSignedNum                   
00001ddf  OLED_ShowString                      
00001e79  OLED_DrawPoint                       
00001f09  SYSCFG_DL_PWM_0_init                 
00001f95  SYSCFG_DL_initPower                  
00002021  __aeabi_fmul                         
00002021  __mulsf3                             
000020ad  OLED_Refresh                         
000021b5  __aeabi_fdiv                         
000021b5  __divsf3                             
00002239  __TI_decompress_lzss                 
000022b5  motor_pid_init                       
0000232d  __gedf2                              
0000232d  __gtdf2                              
000023a1  No_MCU_Ganv_Sensor_Init_Frist        
00002415  OLED_WR_Byte                         
00002481  convertAnalogToDigital               
000024ed  Key_1                                
00002555  __cmpdf2                             
00002555  __eqdf2                              
00002555  __ledf2                              
00002555  __ltdf2                              
00002555  __nedf2                              
000025bd  pid_init                             
00002621  __aeabi_dcmpeq                       
00002635  __aeabi_dcmplt                       
00002649  __aeabi_dcmple                       
0000265d  __aeabi_dcmpge                       
00002671  __aeabi_dcmpgt                       
00002685  __aeabi_fcmpeq                       
00002699  __aeabi_fcmplt                       
000026ad  __aeabi_fcmple                       
000026c1  __aeabi_fcmpge                       
000026d5  __aeabi_fcmpgt                       
000026e9  OLED_Clear                           
00002749  DL_I2C_fillControllerTXFIFO          
000027a9  SYSCFG_DL_I2C_OLED_init              
00002801  SYSCFG_DL_UART_0_init                
000029d9  __aeabi_d2iz                         
000029d9  __fixdfsi                            
00002a23  adc_getValue                         
00002a6d  DL_UART_init                         
00002ab5  OLED_DisplayTurn                     
00002afd  SYSCFG_DL_ADC12_0_init               
00002b45  SYSCFG_DL_SYSCTL_init                
00002b8d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002bd1  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002c15  DL_ADC12_setClockConfig              
00002c55  Key                                  
00002c95  UART0_IRQHandler                     
00002cd5  __aeabi_uidiv                        
00002cd5  __aeabi_uidivmod                     
00002d8d  Get_Anolog_Value                     
00002dc9  __aeabi_i2f                          
00002dc9  __floatsisf                          
00002e05  __gesf2                              
00002e05  __gtsf2                              
00002e41  __TI_auto_init_nobinit_nopinit       
00002e7d  __cmpsf2                             
00002e7d  __eqsf2                              
00002e7d  __lesf2                              
00002e7d  __ltsf2                              
00002e7d  __nesf2                              
00002eb9  __muldsi3                            
00002ef5  __aeabi_f2iz                         
00002ef5  __fixsfsi                            
00002f61  OLED_ColorTurn                       
00002f95  SYSCFG_DL_TIMER_0_init               
00002fc9  SYSCFG_DL_init                       
0000302d  OLED_Pow                             
0000305d  SysTick_Handler                      
000030e5  __aeabi_i2d                          
000030e5  __floatsidf                          
000031d9  _c_int00_noargs                      
00003227  DL_I2C_setClockConfig                
0000324d  __aeabi_ui2d                         
0000324d  __floatunsidf                        
000032b1  delay_ms                             
000032d1  motor_set_target_speed               
00003445  DL_Timer_setCaptCompUpdateMethod     
00003461  DL_Timer_setClockConfig              
000036a9  DL_Timer_setCaptureCompareOutCtl     
00003855  Key_Init_Debounce                    
0000388d  DL_UART_setClockConfig               
0000389f  TI_memcpy_small                      
000038b1  __TI_decompress_none                 
000038e5  DL_Timer_setCaptureCompareValue      
000038f5  Key_System_Tick_Inc                  
00003905  __TI_zero_init                       
00003915  Get_Digtal_For_User                  
00003923  TI_memset_small                      
00003931  SYSCFG_DL_SYSTICK_init               
0000393d  __aeabi_memclr                       
0000393d  __aeabi_memclr4                      
0000393d  __aeabi_memclr8                      
00003949  get_systicks                         
00003955  scheduler_init                       
00003961  DL_Common_delayCycles                
0000396d  __aeabi_memcpy                       
0000396d  __aeabi_memcpy4                      
0000396d  __aeabi_memcpy8                      
00003975  abort                                
0000397b  ADC0_IRQHandler                      
0000397b  ADC1_IRQHandler                      
0000397b  AES_IRQHandler                       
0000397b  CANFD0_IRQHandler                    
0000397b  DAC0_IRQHandler                      
0000397b  DMA_IRQHandler                       
0000397b  Default_Handler                      
0000397b  GROUP0_IRQHandler                    
0000397b  HardFault_Handler                    
0000397b  I2C0_IRQHandler                      
0000397b  I2C1_IRQHandler                      
0000397b  NMI_Handler                          
0000397b  PendSV_Handler                       
0000397b  RTC_IRQHandler                       
0000397b  SPI0_IRQHandler                      
0000397b  SPI1_IRQHandler                      
0000397b  SVC_Handler                          
0000397b  TIMA0_IRQHandler                     
0000397b  TIMA1_IRQHandler                     
0000397b  TIMG12_IRQHandler                    
0000397b  TIMG6_IRQHandler                     
0000397b  TIMG7_IRQHandler                     
0000397b  TIMG8_IRQHandler                     
0000397b  UART1_IRQHandler                     
0000397b  UART2_IRQHandler                     
0000397b  UART3_IRQHandler                     
0000397e  C$$EXIT                              
0000397f  HOSTexit                             
00003983  Reset_Handler                        
00003987  _system_pre_init                     
00003990  asc2_2412                            
000046ec  asc2_1608                            
00004cdc  asc2_1206                            
00005150  asc2_0806                            
00005410  __TI_Handler_Table_Base              
0000541c  __TI_Handler_Table_Limit             
00005424  __TI_CINIT_Base                      
00005434  __TI_CINIT_Limit                     
00005434  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  motor_a_pid                          
20200564  motor_b_pid                          
2020058c  Flag_stop                            
20200590  Flag_stop1                           
20200594  Get_Encoder_countA                   
20200598  Get_Encoder_countB                   
2020059c  encoderA_cnt                         
202005a0  encoderB_cnt                         
202005a4  gpio_interrup1                       
202005a8  gpio_interrup2                       
202005ac  task_num                             
202005b0  rx_buff                              
202006b0  uart_rx_buffer                       
20200730  Anolog                               
20200740  black                                
20200750  white                                
20200760  key1_ctrl                            
2020076c  D_Num                                
20200778  Run                                  
2020077c  delay_times                          
20200794  uart_rx_index                        
20200795  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[221 symbols]
