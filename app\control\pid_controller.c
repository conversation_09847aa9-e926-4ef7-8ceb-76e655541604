#include "pid_controller.h"
#include "motor.h"
#include "encoder.h"
#include <math.h>

// 电机PID控制器实例
pid_controller_t motor_a_pid;  // A电机(右侧)PID控制器
pid_controller_t motor_b_pid;  // B电机(左侧)PID控制器

// 电机目标速度
static int32_t target_speed_a = 0;
static int32_t target_speed_b = 0;

// 电机当前速度(编码器增量)
static int32_t last_encoder_a = 0;
static int32_t last_encoder_b = 0;

/**
 * @brief PID控制器初始化
 */
void pid_init(pid_controller_t* pid, float kp, float ki, float kd, 
              float output_max, float output_min, float dt)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    
    pid->error_sum = 0.0f;
    pid->last_error = 0.0f;
    
    pid->output_max = output_max;
    pid->output_min = output_min;
    
    // 默认积分限幅为输出限幅的80%
    pid->integral_max = output_max * 0.8f;
    pid->integral_min = output_min * 0.8f;
    
    pid->dt = dt;
}

/**
 * @brief PID控制计算
 */
float pid_calculate(pid_controller_t* pid, float target, float current)
{
    // 计算误差
    float error = target - current;
    
    // 比例项
    float p_term = pid->kp * error;
    
    // 积分项
    pid->error_sum += error * pid->dt;
    
    // 积分限幅
    if (pid->error_sum > pid->integral_max) {
        pid->error_sum = pid->integral_max;
    } else if (pid->error_sum < pid->integral_min) {
        pid->error_sum = pid->integral_min;
    }
    
    float i_term = pid->ki * pid->error_sum;
    
    // 微分项
    float d_term = pid->kd * (error - pid->last_error) / pid->dt;
    
    // 更新上次误差
    pid->last_error = error;
    
    // 计算输出
    float output = p_term + i_term + d_term;
    
    // 输出限幅
    if (output > pid->output_max) {
        output = pid->output_max;
    } else if (output < pid->output_min) {
        output = pid->output_min;
    }
    
    return output;
}

/**
 * @brief 重置PID控制器
 */
void pid_reset(pid_controller_t* pid)
{
    pid->error_sum = 0.0f;
    pid->last_error = 0.0f;
}

/**
 * @brief 设置PID参数
 */
void pid_set_params(pid_controller_t* pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
}

/**
 * @brief 设置输出限制
 */
void pid_set_output_limits(pid_controller_t* pid, float output_max, float output_min)
{
    pid->output_max = output_max;
    pid->output_min = output_min;
}

/**
 * @brief 设置积分限幅
 */
void pid_set_integral_limits(pid_controller_t* pid, float integral_max, float integral_min)
{
    pid->integral_max = integral_max;
    pid->integral_min = integral_min;
}

/**
 * @brief 电机PID控制器初始化
 */
void motor_pid_init(void)
{
    // 初始化A电机PID控制器(右侧电机)
    // PID参数需要根据实际测试调优
    pid_init(&motor_a_pid, 
             50.0f,    // kp: 比例系数
             0.5f,     // ki: 积分系数  
             2.0f,     // kd: 微分系数
             8000.0f,  // output_max: PWM最大值
             -8000.0f, // output_min: PWM最小值
             0.01f);   // dt: 10ms采样周期
    
    // 初始化B电机PID控制器(左侧电机)
    pid_init(&motor_b_pid,
             50.0f,    // kp: 比例系数
             0.5f,     // ki: 积分系数
             2.0f,     // kd: 微分系数  
             8000.0f,  // output_max: PWM最大值
             -8000.0f, // output_min: PWM最小值
             0.01f);   // dt: 10ms采样周期
    
    // 初始化编码器历史值
    last_encoder_a = Get_Encoder_countA;
    last_encoder_b = Get_Encoder_countB;
}

/**
 * @brief 电机PID控制任务(10ms周期调用)
 */
void motor_pid_task(void)
{
    // 获取当前编码器值
    int32_t current_encoder_a = Get_Encoder_countA;
    int32_t current_encoder_b = Get_Encoder_countB;
    
    // 计算速度(编码器增量)
    int32_t speed_a = current_encoder_a - last_encoder_a;
    int32_t speed_b = current_encoder_b - last_encoder_b;
    
    // 更新编码器历史值
    last_encoder_a = current_encoder_a;
    last_encoder_b = current_encoder_b;
    
    // PID控制计算
    float pwm_a = pid_calculate(&motor_a_pid, (float)target_speed_a, (float)speed_a);
    float pwm_b = pid_calculate(&motor_b_pid, (float)target_speed_b, (float)speed_b);
    
    // 输出PWM控制信号
    Set_PWM((int)pwm_a, (int)pwm_b);
}

/**
 * @brief 设置电机目标速度
 */
void motor_set_target_speed(int32_t target_speed_a_val, int32_t target_speed_b_val)
{
    target_speed_a = target_speed_a_val;
    target_speed_b = target_speed_b_val;
}
