#include "motor.h"
#include "Ganway.h"
#include "bsp_system.h"

// 路径控制器实例
static path_controller_t path_ctrl;

// 路径决策查找表
static const path_decision_t path_decision_table[] = {
    // 传感器模式, 下一状态, 左速度, 右速度, 描述
    {0x00, PATH_STATE_LOST, 0, 0, "All Black - Line Lost"},                    // 00000000
    {0x18, PATH_STATE_STRAIGHT, BASE_SPEED, BASE_SPEED, "Center - Straight"},  // 00011000
    {0x08, PATH_STATE_TURN_RIGHT, BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF, "Right Center"}, // 00001000
    {0x10, PATH_STATE_TURN_LEFT, BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF, "Left Center"},   // 00010000
    
    // 右转系列
    {0x04, PATH_STATE_TURN_RIGHT, BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF, "Right Turn"},   // 00000100
    {0x0C, PATH_STATE_TURN_RIGHT, BASE_SPEED + TURN_SPEED_DIFF/2, BASE_SPEED - TURN_SPEED_DIFF/2, "Right Slight"}, // 00001100
    {0x02, PATH_STATE_SHARP_RIGHT, BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF, "Sharp Right"}, // 00000010
    {0x01, PATH_STATE_SHARP_RIGHT, LARGE_TURN_SPEED, -LARGE_TURN_SPEED/2, "Far Right"},                        // 00000001
    {0x03, PATH_STATE_SHARP_RIGHT, BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF, "Right Edge"},  // 00000011
    
    // 左转系列
    {0x20, PATH_STATE_TURN_LEFT, BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF, "Left Turn"},     // 00100000
    {0x30, PATH_STATE_TURN_LEFT, BASE_SPEED - TURN_SPEED_DIFF/2, BASE_SPEED + TURN_SPEED_DIFF/2, "Left Slight"}, // 00110000
    {0x40, PATH_STATE_SHARP_LEFT, BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF, "Sharp Left"},   // 01000000
    {0x80, PATH_STATE_SHARP_LEFT, -LARGE_TURN_SPEED/2, LARGE_TURN_SPEED, "Far Left"},                          // 10000000
    {0xC0, PATH_STATE_SHARP_LEFT, BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF, "Left Edge"},    // 11000000
    
    // 特殊情况
    {0xF0, PATH_STATE_SHARP_LEFT, -LARGE_TURN_SPEED, LARGE_TURN_SPEED, "Multi Left"},                          // 11110000
    {0x0F, PATH_STATE_SHARP_RIGHT, LARGE_TURN_SPEED, -LARGE_TURN_SPEED, "Multi Right"},                        // 00001111
    {0xFF, PATH_STATE_STOP, 0, 0, "All White - Stop"},                                                         // 11111111
};

#define PATH_DECISION_TABLE_SIZE (sizeof(path_decision_table) / sizeof(path_decision_table[0]))

/**
 * @brief 路径控制器初始化
 */
void path_controller_init(void)
{
    path_ctrl.current_state = PATH_STATE_STRAIGHT;
    path_ctrl.last_state = PATH_STATE_STRAIGHT;
    path_ctrl.state_timer = 0;
    path_ctrl.last_sensor_data = 0x18; // 默认中间两个传感器
    path_ctrl.lost_line_count = 0;
    path_ctrl.search_count = 0;
}

/**
 * @brief 查找路径决策
 * @param sensor_data 传感器数据
 * @return const path_decision_t* 决策表项指针，NULL表示未找到
 */
static const path_decision_t* find_path_decision(uint8_t sensor_data)
{
    for (uint16_t i = 0; i < PATH_DECISION_TABLE_SIZE; i++) {
        if (path_decision_table[i].sensor_pattern == sensor_data) {
            return &path_decision_table[i];
        }
    }
    return NULL; // 未找到匹配项
}

/**
 * @brief 路径状态机处理
 */
path_event_t path_state_machine(uint8_t sensor_data)
{
    path_event_t event = PATH_EVENT_NONE;
    const path_decision_t* decision = find_path_decision(sensor_data);
    
    // 更新状态计时器
    path_ctrl.state_timer++;
    
    switch (path_ctrl.current_state) {
        case PATH_STATE_STRAIGHT:
        case PATH_STATE_TURN_LEFT:
        case PATH_STATE_TURN_RIGHT:
        case PATH_STATE_SHARP_LEFT:
        case PATH_STATE_SHARP_RIGHT:
            if (sensor_data == 0x00) { // 全黑，丢线
                path_ctrl.lost_line_count++;
                if (path_ctrl.lost_line_count >= LOST_LINE_THRESHOLD) {
                    path_ctrl.current_state = PATH_STATE_SEARCH;
                    path_ctrl.search_count = 0;
                    event = PATH_EVENT_LINE_LOST;
                }
            } else {
                path_ctrl.lost_line_count = 0; // 重置丢线计数
                if (decision != NULL) {
                    path_ctrl.last_state = path_ctrl.current_state;
                    path_ctrl.current_state = decision->next_state;
                    path_ctrl.state_timer = 0;
                    event = PATH_EVENT_LINE_FOUND;
                }
            }
            break;
            
        case PATH_STATE_SEARCH:
            path_ctrl.search_count++;
            if (sensor_data != 0x00) { // 找到线
                path_ctrl.current_state = PATH_STATE_STRAIGHT;
                path_ctrl.lost_line_count = 0;
                path_ctrl.search_count = 0;
                event = PATH_EVENT_LINE_FOUND;
            } else if (path_ctrl.search_count >= SEARCH_TIMEOUT) {
                // 搜索超时，继续搜索或停止
                path_ctrl.search_count = 0;
                event = PATH_EVENT_SEARCH_TIMEOUT;
            }
            break;
            
        case PATH_STATE_LOST:
            // 丢线状态处理
            if (sensor_data != 0x00) {
                path_ctrl.current_state = PATH_STATE_STRAIGHT;
                event = PATH_EVENT_LINE_FOUND;
            }
            break;
            
        case PATH_STATE_STOP:
            // 停止状态，等待外部命令
            break;
            
        default:
            path_ctrl.current_state = PATH_STATE_STRAIGHT;
            break;
    }
    
    path_ctrl.last_sensor_data = sensor_data;
    return event;
}

void Way(unsigned char x)
{
    // 转换传感器数据格式(取反逻辑)
    uint8_t sensor_data = (~x) & 0xFF;

    // 执行状态机处理
    path_event_t event = path_state_machine(sensor_data);

    // 根据当前状态设置电机速度
    const path_decision_t* decision = find_path_decision(sensor_data);

    if (path_ctrl.current_state == PATH_STATE_SEARCH) {
        // 搜索状态：原地右转
        motor_set_target_speed(LARGE_TURN_SPEED, -LARGE_TURN_SPEED);
    } else if (path_ctrl.current_state == PATH_STATE_STOP) {
        // 停止状态
        motor_set_target_speed(0, 0);
    } else if (decision != NULL) {
        // 根据决策表设置速度
        motor_set_target_speed(decision->speed_left, decision->speed_right);
    } else {
        // 默认情况：直行
        motor_set_target_speed(BASE_SPEED, BASE_SPEED);
    }
}

/**
 * @brief 获取当前路径状态
 */
path_state_t get_current_path_state(void)
{
    return path_ctrl.current_state;
}

/**
 * @brief 获取路径状态描述
 */
const char* get_path_state_description(path_state_t state)
{
    switch (state) {
        case PATH_STATE_STRAIGHT:    return "Straight";
        case PATH_STATE_TURN_LEFT:   return "Turn Left";
        case PATH_STATE_TURN_RIGHT:  return "Turn Right";
        case PATH_STATE_SHARP_LEFT:  return "Sharp Left";
        case PATH_STATE_SHARP_RIGHT: return "Sharp Right";
        case PATH_STATE_LOST:        return "Line Lost";
        case PATH_STATE_STOP:        return "Stopped";
        case PATH_STATE_SEARCH:      return "Searching";
        default:                     return "Unknown";
    }
}
