#include "motor.h"
#include "Ganway.h"
#include "bsp_system.h"

void Way(unsigned char x)
{

    unsigned char sensor0 = 1-(x >> 0) & 0x01;
    unsigned char sensor1 = 1-(x >> 1) & 0x01;
    unsigned char sensor2 = 1-(x >> 2) & 0x01;
    unsigned char sensor3 = 1-(x >> 3) & 0x01;
    unsigned char sensor4 = 1-(x >> 4) & 0x01;
    unsigned char sensor5 = 1-(x >> 5) & 0x01;
    unsigned char sensor6 = 1-(x >> 6) & 0x01;
    unsigned char sensor7 = 1-(x >> 7) & 0x01;

    // 全黑 - 丢线，执行搜索动作
    if(sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        motor_set_target_speed(LARGE_TURN_SPEED, -LARGE_TURN_SPEED); // 原地右转搜索
    }
    // 左侧多传感器检测到线 - 大幅左转
    else if(sensor0 == 1 && sensor1 == 1 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        motor_set_target_speed(-LARGE_TURN_SPEED, LARGE_TURN_SPEED); // 大幅左转
    }





    // 中间两个传感器检测到线 - 直行
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED, BASE_SPEED); // 直行
    }
    // 右侧中心传感器检测到线 - 小幅右转
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF); // 右转
    }
    // 左侧中心传感器检测到线 - 小幅左转
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF); // 左转
    }







    // 右侧外围传感器检测到线 - 小幅右转
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF/2, BASE_SPEED - TURN_SPEED_DIFF/2); // 小幅右转
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF/2, BASE_SPEED - TURN_SPEED_DIFF/2); // 小幅右转
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF); // 右转
    }
    // 最右侧传感器检测到线 - 大幅右转
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 1) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF); // 大幅右转
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 1) {
        motor_set_target_speed(BASE_SPEED + TURN_SPEED_DIFF, BASE_SPEED - TURN_SPEED_DIFF); // 右转
    }

    // 最左侧传感器检测到线 - 大幅左转
    else if (sensor0 == 1 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF); // 大幅左转
    }
    else if (sensor0 == 1 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF); // 左转
    }
    // 左侧外围传感器检测到线 - 小幅左转
    else if (sensor0 == 0 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF, BASE_SPEED + TURN_SPEED_DIFF); // 左转
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF/2, BASE_SPEED + TURN_SPEED_DIFF/2); // 小幅左转
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        motor_set_target_speed(BASE_SPEED - TURN_SPEED_DIFF/2, BASE_SPEED + TURN_SPEED_DIFF/2); // 小幅左转
    }
    // 默认情况 - 保持当前状态
    else {
        motor_set_target_speed(BASE_SPEED, BASE_SPEED); // 直行
    }





 
    
}
