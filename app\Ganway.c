#include "motor.h"
#include "Ganway.h"
#include "bsp_system.h"

void Way(unsigned char x)
{

    unsigned char sensor0 = 1-(x >> 0) & 0x01;
    unsigned char sensor1 = 1-(x >> 1) & 0x01;
    unsigned char sensor2 = 1-(x >> 2) & 0x01;
    unsigned char sensor3 = 1-(x >> 3) & 0x01;
    unsigned char sensor4 = 1-(x >> 4) & 0x01;
    unsigned char sensor5 = 1-(x >> 5) & 0x01;
    unsigned char sensor6 = 1-(x >> 6) & 0x01;
    unsigned char sensor7 = 1-(x >> 7) & 0x01;

    if(sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        Set_PWM(100,100);
        delay_ms(1);
        Set_PWM(5000,-6000);
        delay_ms(1);
    }
    if(sensor0 == 1 && sensor1 == 1 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        Set_PWM(10,10);
        delay_ms(2);
        Set_PWM(4000,-5000);
    }





    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Set_PWM(7000,7000);
    }

    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
 
       Right_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
 
        Left_Control();
    }







    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {

        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {
        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 0) {
        Right_Little_Control();
    }



    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 1) {
        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 1) {
        Right_Control();
    }

    else if (sensor0 == 1 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
       Left_Little_Control();
    }
    else if (sensor0 == 1 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Control();
    }


    else if (sensor0 == 0 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }





 
    
}
// void Set_PWM(int pwmA,int pwmB);
// void Left_Control(void);
// void Right_Control(void);
// void Left_Large_Control(void);
// void Right_Large_Control(void);
