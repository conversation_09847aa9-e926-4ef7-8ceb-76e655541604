#ifndef _PID_CONTROLLER_H
#define _PID_CONTROLLER_H

#include "stdint.h"

/**
 * @brief PID控制器结构体
 * @details 包含PID参数、误差历史和输出限制
 */
typedef struct {
    // PID参数
    float kp;           // 比例系数
    float ki;           // 积分系数  
    float kd;           // 微分系数
    
    // 误差历史
    float error_sum;    // 误差积分累计
    float last_error;   // 上次误差值
    
    // 输出限制
    float output_max;   // 输出上限
    float output_min;   // 输出下限
    
    // 积分限幅
    float integral_max; // 积分项上限
    float integral_min; // 积分项下限
    
    // 采样时间
    float dt;           // 采样周期(秒)
} pid_controller_t;

/**
 * @brief PID控制器初始化
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @param output_max 输出上限
 * @param output_min 输出下限
 * @param dt 采样周期(秒)
 */
void pid_init(pid_controller_t* pid, float kp, float ki, float kd, 
              float output_max, float output_min, float dt);

/**
 * @brief PID控制计算
 * @param pid PID控制器指针
 * @param target 目标值
 * @param current 当前值
 * @return 控制输出值
 */
float pid_calculate(pid_controller_t* pid, float target, float current);

/**
 * @brief 重置PID控制器
 * @param pid PID控制器指针
 */
void pid_reset(pid_controller_t* pid);

/**
 * @brief 设置PID参数
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void pid_set_params(pid_controller_t* pid, float kp, float ki, float kd);

/**
 * @brief 设置输出限制
 * @param pid PID控制器指针
 * @param output_max 输出上限
 * @param output_min 输出下限
 */
void pid_set_output_limits(pid_controller_t* pid, float output_max, float output_min);

/**
 * @brief 设置积分限幅
 * @param pid PID控制器指针
 * @param integral_max 积分项上限
 * @param integral_min 积分项下限
 */
void pid_set_integral_limits(pid_controller_t* pid, float integral_max, float integral_min);

// 电机PID控制器实例声明
extern pid_controller_t motor_a_pid;  // A电机(右侧)PID控制器
extern pid_controller_t motor_b_pid;  // B电机(左侧)PID控制器

/**
 * @brief 电机PID控制器初始化
 */
void motor_pid_init(void);

/**
 * @brief 电机PID控制任务(10ms周期调用)
 */
void motor_pid_task(void);

/**
 * @brief 设置电机目标速度
 * @param target_speed_a A电机目标速度(编码器计数/10ms)
 * @param target_speed_b B电机目标速度(编码器计数/10ms)
 */
void motor_set_target_speed(int32_t target_speed_a, int32_t target_speed_b);

#endif // _PID_CONTROLLER_H
