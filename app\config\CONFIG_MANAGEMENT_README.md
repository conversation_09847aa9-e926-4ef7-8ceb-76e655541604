# 系统配置管理模块使用说明

## 概述
本模块实现了统一的系统配置管理，将原有的硬编码参数集中管理，支持运行时参数调整、配置保存和恢复等功能。

## 主要特性
- ✅ 统一配置管理：集中管理所有系统参数
- ✅ 运行时调整：支持动态修改参数
- ✅ 配置验证：自动验证和修复异常参数
- ✅ 持久化存储：支持Flash存储(可扩展)
- ✅ 默认配置：提供合理的默认参数值

## 配置结构

### 传感器配置 (sensor_config_t)
```c
typedef struct {
    uint16_t black_values[8];    // 黑线校准值
    uint16_t white_values[8];    // 白线校准值
    uint16_t threshold_values[8]; // 阈值
    uint8_t filter_enable;       // 滤波使能
    uint8_t direction;           // 传感器方向
} sensor_config_t;
```

### PID配置 (pid_config_t)
```c
typedef struct {
    float motor_a_kp, motor_a_ki, motor_a_kd;  // 电机A PID参数
    float motor_b_kp, motor_b_ki, motor_b_kd;  // 电机B PID参数
    float output_max, output_min;              // 输出限制
    float integral_max, integral_min;          // 积分限幅
    float sample_time;                         // 采样周期
} pid_config_t;
```

### 电机配置 (motor_config_t)
```c
typedef struct {
    int16_t base_speed;         // 基础速度
    int16_t turn_speed_diff;    // 转弯速度差
    int16_t large_turn_speed;   // 大转弯速度
    int16_t max_speed;          // 最大速度
    int16_t min_speed;          // 最小速度
    uint8_t motor_direction_a;  // 电机A方向
    uint8_t motor_direction_b;  // 电机B方向
} motor_config_t;
```

### 路径配置 (path_config_t)
```c
typedef struct {
    uint16_t lost_line_threshold;   // 丢线判断阈值
    uint16_t search_timeout;        // 搜索超时时间
    uint8_t search_direction;       // 搜索方向
    uint8_t auto_calibration;       // 自动校准使能
} path_config_t;
```

## 使用方法

### 1. 系统初始化
```c
// 在main函数中调用
system_config_init();
```

### 2. 获取配置
```c
// 获取特定配置
sensor_config_t* sensor_cfg = get_sensor_config();
pid_config_t* pid_cfg = get_pid_config();
motor_config_t* motor_cfg = get_motor_config();
path_config_t* path_cfg = get_path_config();

// 获取完整配置
system_config_t* config = get_system_config();
```

### 3. 运行时参数调整
```c
// 调整PID参数
config_adjust_pid_param(2, CONFIG_PARAM_PID_KP, 5.0f); // 两个电机Kp+5.0

// 调整电机参数
config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, 10); // 基础速度+10

// 调整路径参数
config_adjust_path_param(CONFIG_PARAM_PATH_LOST_THRESHOLD, 1); // 丢线阈值+1
```

### 4. 传感器校准
```c
// 手动校准
uint16_t black_data[8] = {200, 1100, 1200, 900, 850, 900, 950, 1000};
uint16_t white_data[8] = {3200, 4200, 4300, 3800, 4100, 3500, 3900, 4100};

config_calibrate_sensor(black_data, 0); // 黑线校准
config_calibrate_sensor(white_data, 1); // 白线校准
```

### 5. 配置保存和恢复
```c
// 保存当前配置
config_save_current();

// 恢复默认配置
config_reset_to_default();

// 验证和修复配置
config_validate_and_fix();
```

## 默认配置值

### 传感器默认值
- **黑线校准值**: {173, 1001, 1009, 869, 838, 869, 875, 914}
- **白线校准值**: {3106, 4066, 4091, 3714, 4055, 3387, 3811, 3971}
- **滤波使能**: 开启

### PID默认值
- **Kp**: 50.0 (比例系数)
- **Ki**: 0.5 (积分系数)
- **Kd**: 2.0 (微分系数)
- **输出限制**: ±8000
- **积分限幅**: ±6400

### 电机默认值
- **基础速度**: 80
- **转弯速度差**: 40
- **大转弯速度**: 120

### 路径默认值
- **丢线阈值**: 10 (100ms)
- **搜索超时**: 50 (500ms)

## 参数调整指南

### PID参数调优
1. **Kp调整**: 影响响应速度，范围0.0-200.0
2. **Ki调整**: 消除稳态误差，范围0.0-10.0
3. **Kd调整**: 减少超调，范围0.0-50.0

### 速度参数调优
1. **基础速度**: 影响整体速度，范围10-300
2. **转弯差值**: 影响转弯灵敏度，范围5-100
3. **大转弯速度**: 影响急转弯能力，范围50-400

### 路径参数调优
1. **丢线阈值**: 影响丢线检测敏感度，范围1-100
2. **搜索超时**: 影响搜索时间，范围10-200

## 配置验证

### 自动验证规则
- PID参数范围检查
- 速度参数合理性检查
- 阈值参数边界检查
- 配置完整性验证

### 错误处理
- 超出范围的参数自动修正为默认值
- 损坏的配置自动恢复为默认配置
- 提供配置状态反馈

## 调试功能

### 配置信息显示
```c
// 打印配置信息到串口或OLED
config_print_info(uart_send_string);

// 获取状态字符串
char status[256];
config_get_status_string(status, sizeof(status));
```

### 配置导出
```c
// 导出配置为字符串
char export_str[512];
config_export_to_string(export_str, sizeof(export_str));
```

## 扩展功能

### Flash存储支持
- 配置自动保存到Flash
- 上电自动加载配置
- 配置损坏时自动恢复

### 远程配置
- 支持串口命令配置
- 支持无线配置传输
- 配置文件导入导出

## 性能特点

### 内存使用
- 配置结构体大小: ~200字节
- 运行时内存开销: 最小
- Flash存储需求: ~1KB

### 访问性能
- 配置读取: O(1)时间复杂度
- 参数修改: 即时生效
- 配置验证: 快速完成

## 使用示例

### 基本使用流程
```c
int main(void) {
    // 1. 系统初始化
    SYSCFG_DL_init();
    system_config_init();
    
    // 2. 获取配置并初始化模块
    sensor_config_t* sensor_cfg = get_sensor_config();
    No_MCU_Ganv_Sensor_Init(&sensor, sensor_cfg->white_values, sensor_cfg->black_values);
    
    motor_pid_init(); // PID控制器会自动使用配置参数
    
    // 3. 主循环
    while(1) {
        // 系统运行...
        
        // 可以在运行时调整参数
        if (button_pressed) {
            config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, 5);
        }
    }
}
```

### 校准流程示例
```c
void calibration_process(void) {
    // 1. 黑线校准
    OLED_ShowString(0, 0, "Place on BLACK line", 12, 1);
    delay_ms(2000);
    
    uint16_t black_values[8];
    for(int i = 0; i < 8; i++) {
        black_values[i] = Get_Analog_value(i);
    }
    config_calibrate_sensor(black_values, 0);
    
    // 2. 白线校准
    OLED_ShowString(0, 16, "Place on WHITE area", 12, 1);
    delay_ms(2000);
    
    uint16_t white_values[8];
    for(int i = 0; i < 8; i++) {
        white_values[i] = Get_Analog_value(i);
    }
    config_calibrate_sensor(white_values, 1);
    
    // 3. 保存配置
    config_save_current();
    OLED_ShowString(0, 32, "Calibration Done!", 12, 1);
}
```

## 总结

配置管理模块提供了完整的参数管理解决方案，显著提升了系统的可配置性和可维护性。通过统一的接口和自动化的管理机制，使得参数调优和系统配置变得更加简单和可靠。
