# 路径决策状态机使用说明

## 概述
本模块实现了基于状态机和查表法的智能路径决策算法，替换了原有的大量硬编码if-else语句，提供更高效、更可维护的路径控制方案。

## 主要特性
- ✅ 状态机模式：清晰的状态转换逻辑
- ✅ 查表法决策：高效的传感器模式匹配
- ✅ 丢线检测：智能的线路丢失处理
- ✅ 搜索机制：自动搜索丢失的线路
- ✅ 无阻塞延时：消除所有delay_ms调用

## 状态定义

### 路径状态 (path_state_t)
- `PATH_STATE_STRAIGHT`: 直行状态
- `PATH_STATE_TURN_LEFT`: 左转状态  
- `PATH_STATE_TURN_RIGHT`: 右转状态
- `PATH_STATE_SHARP_LEFT`: 急左转状态
- `PATH_STATE_SHARP_RIGHT`: 急右转状态
- `PATH_STATE_LOST`: 丢线状态
- `PATH_STATE_SEARCH`: 搜索状态
- `PATH_STATE_STOP`: 停止状态

### 路径事件 (path_event_t)
- `PATH_EVENT_NONE`: 无事件
- `PATH_EVENT_LINE_FOUND`: 找到线路
- `PATH_EVENT_LINE_LOST`: 丢失线路
- `PATH_EVENT_TURN_COMPLETE`: 转弯完成
- `PATH_EVENT_SEARCH_TIMEOUT`: 搜索超时

## 决策表说明

### 传感器模式映射
```
传感器位: 7 6 5 4 3 2 1 0
示例:     0 0 0 1 1 0 0 0  = 0x18 (中间两个传感器)
```

### 主要决策规则
| 传感器模式 | 二进制 | 状态 | 动作 | 描述 |
|-----------|--------|------|------|------|
| 0x00 | 00000000 | LOST | 停止 | 全黑-丢线 |
| 0x18 | 00011000 | STRAIGHT | 直行 | 中间检测 |
| 0x08 | 00001000 | TURN_RIGHT | 右转 | 右中心 |
| 0x10 | 00010000 | TURN_LEFT | 左转 | 左中心 |
| 0x01 | 00000001 | SHARP_RIGHT | 急右转 | 最右侧 |
| 0x80 | 10000000 | SHARP_LEFT | 急左转 | 最左侧 |

## 使用方法

### 1. 初始化
```c
// 在main函数中调用
path_controller_init();
```

### 2. 路径控制
```c
// 在主循环中调用(原Way函数接口保持不变)
Way(sensor_digital_data);
```

### 3. 状态查询
```c
// 获取当前状态
path_state_t current_state = get_current_path_state();

// 获取状态描述
const char* state_desc = get_path_state_description(current_state);
```

## 状态机逻辑

### 正常运行状态
1. **直行/转弯状态**: 根据传感器数据查表决策
2. **丢线检测**: 连续检测到全黑超过阈值时进入搜索状态
3. **状态转换**: 根据传感器模式自动切换状态

### 丢线处理
1. **丢线计数**: 检测到0x00(全黑)时递增计数器
2. **搜索触发**: 计数器达到阈值(100ms)时进入搜索状态
3. **搜索动作**: 原地右转寻找线路
4. **恢复机制**: 找到线路时立即恢复正常状态

### 搜索超时
1. **超时检测**: 搜索时间超过500ms
2. **继续搜索**: 重置计数器继续搜索
3. **可扩展**: 可添加停止或其他策略

## 参数配置

### 时间参数
```c
#define LOST_LINE_THRESHOLD 10    // 丢线判断阈值(100ms)
#define SEARCH_TIMEOUT      50    // 搜索超时(500ms)
```

### 速度参数
```c
#define BASE_SPEED          80    // 基础速度
#define TURN_SPEED_DIFF     40    // 转弯速度差
#define LARGE_TURN_SPEED    120   // 大转弯速度
```

## 性能优势

### 1. 执行效率提升
- **查表法**: O(n)复杂度，n为决策表大小
- **消除分支**: 减少大量条件判断
- **无阻塞**: 消除所有delay_ms调用

### 2. 代码质量改善
- **可维护性**: 决策逻辑集中在表中
- **可扩展性**: 新增传感器模式只需添加表项
- **可读性**: 状态和事件定义清晰

### 3. 功能增强
- **智能丢线**: 自动检测和处理丢线情况
- **状态追踪**: 完整的状态历史记录
- **事件驱动**: 支持事件响应机制

## 调试功能

### 状态监控
```c
// 在OLED或串口显示当前状态
path_state_t state = get_current_path_state();
const char* desc = get_path_state_description(state);
OLED_ShowString(0, 32, (uint8_t*)desc, 12, 1);
```

### 传感器数据分析
```c
// 显示传感器原始数据和转换后数据
uint8_t raw_data = sensor_digital;
uint8_t processed_data = (~raw_data) & 0xFF;
```

## 故障排除

### 常见问题
1. **转弯不灵敏**: 调整TURN_SPEED_DIFF参数
2. **丢线频繁**: 调整LOST_LINE_THRESHOLD参数
3. **搜索时间长**: 调整SEARCH_TIMEOUT参数
4. **状态切换异常**: 检查传感器数据格式

### 调试建议
1. 使用OLED显示当前状态和传感器数据
2. 通过串口输出状态转换日志
3. 测试各种传感器模式的响应
4. 验证丢线和恢复机制

## 扩展建议

1. **自适应参数**: 根据运行环境自动调整参数
2. **学习机制**: 记录和优化常用路径模式
3. **多模式支持**: 支持不同的寻迹模式切换
4. **预测算法**: 基于历史数据预测路径变化
