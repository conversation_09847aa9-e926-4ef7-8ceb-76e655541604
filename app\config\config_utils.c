#include "config_utils.h"
#include "stdio.h"
#include "string.h"

/**
 * @brief 限制数值在指定范围内
 */
static float clamp_float(float value, float min, float max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

static int16_t clamp_int16(int16_t value, int16_t min, int16_t max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

/**
 * @brief 打印当前配置信息
 */
void config_print_info(void (*output_func)(const char*))
{
    char buffer[256];
    system_config_t* config = get_system_config();
    
    if (!output_func) return;
    
    // PID配置
    snprintf(buffer, sizeof(buffer), "PID A: Kp=%.2f Ki=%.2f Kd=%.2f\n", 
             config->pid.motor_a_kp, config->pid.motor_a_ki, config->pid.motor_a_kd);
    output_func(buffer);
    
    snprintf(buffer, sizeof(buffer), "PID B: Kp=%.2f Ki=%.2f Kd=%.2f\n", 
             config->pid.motor_b_kp, config->pid.motor_b_ki, config->pid.motor_b_kd);
    output_func(buffer);
    
    // 电机配置
    snprintf(buffer, sizeof(buffer), "Motor: Base=%d Turn=%d Large=%d\n", 
             config->motor.base_speed, config->motor.turn_speed_diff, config->motor.large_turn_speed);
    output_func(buffer);
    
    // 路径配置
    snprintf(buffer, sizeof(buffer), "Path: Lost=%d Search=%d\n", 
             config->path.lost_line_threshold, config->path.search_timeout);
    output_func(buffer);
}

/**
 * @brief 执行传感器校准
 */
bool config_calibrate_sensor(const uint16_t* sensor_data, uint8_t calibration_type)
{
    if (!sensor_data) return false;
    
    sensor_config_t* sensor_cfg = get_sensor_config();
    
    if (calibration_type == 0) { // 黑线校准
        memcpy(sensor_cfg->black_values, sensor_data, sizeof(sensor_cfg->black_values));
    } else if (calibration_type == 1) { // 白线校准
        memcpy(sensor_cfg->white_values, sensor_data, sizeof(sensor_cfg->white_values));
    } else {
        return false;
    }
    
    // 重新计算阈值
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_cfg->threshold_values[i] = 
            (sensor_cfg->black_values[i] + sensor_cfg->white_values[i]) / 2;
    }
    
    return true;
}

/**
 * @brief 调整PID参数
 */
bool config_adjust_pid_param(uint8_t motor_id, uint8_t param_type, float delta)
{
    pid_config_t* pid_cfg = get_pid_config();
    
    if (motor_id == 0 || motor_id == 2) { // 电机A或两个电机
        switch (param_type) {
            case CONFIG_PARAM_PID_KP:
                pid_cfg->motor_a_kp = clamp_float(pid_cfg->motor_a_kp + delta, PID_KP_MIN, PID_KP_MAX);
                break;
            case CONFIG_PARAM_PID_KI:
                pid_cfg->motor_a_ki = clamp_float(pid_cfg->motor_a_ki + delta, PID_KI_MIN, PID_KI_MAX);
                break;
            case CONFIG_PARAM_PID_KD:
                pid_cfg->motor_a_kd = clamp_float(pid_cfg->motor_a_kd + delta, PID_KD_MIN, PID_KD_MAX);
                break;
            default:
                return false;
        }
    }
    
    if (motor_id == 1 || motor_id == 2) { // 电机B或两个电机
        switch (param_type) {
            case CONFIG_PARAM_PID_KP:
                pid_cfg->motor_b_kp = clamp_float(pid_cfg->motor_b_kp + delta, PID_KP_MIN, PID_KP_MAX);
                break;
            case CONFIG_PARAM_PID_KI:
                pid_cfg->motor_b_ki = clamp_float(pid_cfg->motor_b_ki + delta, PID_KI_MIN, PID_KI_MAX);
                break;
            case CONFIG_PARAM_PID_KD:
                pid_cfg->motor_b_kd = clamp_float(pid_cfg->motor_b_kd + delta, PID_KD_MIN, PID_KD_MAX);
                break;
            default:
                return false;
        }
    }
    
    return true;
}

/**
 * @brief 调整电机速度参数
 */
bool config_adjust_motor_param(uint8_t param_type, int16_t delta)
{
    motor_config_t* motor_cfg = get_motor_config();
    
    switch (param_type) {
        case CONFIG_PARAM_MOTOR_BASE_SPEED:
            motor_cfg->base_speed = clamp_int16(motor_cfg->base_speed + delta, SPEED_MIN, SPEED_MAX);
            break;
        case CONFIG_PARAM_MOTOR_TURN_DIFF:
            motor_cfg->turn_speed_diff = clamp_int16(motor_cfg->turn_speed_diff + delta, TURN_DIFF_MIN, TURN_DIFF_MAX);
            break;
        case CONFIG_PARAM_MOTOR_LARGE_TURN:
            motor_cfg->large_turn_speed = clamp_int16(motor_cfg->large_turn_speed + delta, LARGE_TURN_MIN, LARGE_TURN_MAX);
            break;
        default:
            return false;
    }
    
    return true;
}

/**
 * @brief 调整路径控制参数
 */
bool config_adjust_path_param(uint8_t param_type, int16_t delta)
{
    path_config_t* path_cfg = get_path_config();
    
    switch (param_type) {
        case CONFIG_PARAM_PATH_LOST_THRESHOLD:
            path_cfg->lost_line_threshold = clamp_int16(path_cfg->lost_line_threshold + delta, 
                                                       LOST_THRESHOLD_MIN, LOST_THRESHOLD_MAX);
            break;
        case CONFIG_PARAM_PATH_SEARCH_TIMEOUT:
            path_cfg->search_timeout = clamp_int16(path_cfg->search_timeout + delta, 
                                                  SEARCH_TIMEOUT_MIN, SEARCH_TIMEOUT_MAX);
            break;
        default:
            return false;
    }
    
    return true;
}

/**
 * @brief 保存当前配置
 */
bool config_save_current(void)
{
    return config_save_to_flash();
}

/**
 * @brief 重置为默认配置
 */
bool config_reset_to_default(void)
{
    config_restore_defaults();
    return true;
}

/**
 * @brief 获取配置状态字符串
 */
int config_get_status_string(char* buffer, size_t buffer_size)
{
    if (!buffer || buffer_size == 0) return 0;
    
    system_config_t* config = get_system_config();
    
    return snprintf(buffer, buffer_size, 
                   "Config: PID(%.1f,%.1f,%.1f) Motor(%d,%d,%d) Path(%d,%d)",
                   config->pid.motor_a_kp, config->pid.motor_a_ki, config->pid.motor_a_kd,
                   config->motor.base_speed, config->motor.turn_speed_diff, config->motor.large_turn_speed,
                   config->path.lost_line_threshold, config->path.search_timeout);
}

/**
 * @brief 验证并修复配置
 */
bool config_validate_and_fix(void)
{
    system_config_t* config = get_system_config();
    bool fixed = false;
    
    // 修复PID参数
    if (config->pid.motor_a_kp < PID_KP_MIN || config->pid.motor_a_kp > PID_KP_MAX) {
        config->pid.motor_a_kp = DEFAULT_PID_KP;
        fixed = true;
    }
    
    if (config->pid.motor_a_ki < PID_KI_MIN || config->pid.motor_a_ki > PID_KI_MAX) {
        config->pid.motor_a_ki = DEFAULT_PID_KI;
        fixed = true;
    }
    
    if (config->pid.motor_a_kd < PID_KD_MIN || config->pid.motor_a_kd > PID_KD_MAX) {
        config->pid.motor_a_kd = DEFAULT_PID_KD;
        fixed = true;
    }
    
    // 修复电机参数
    if (config->motor.base_speed < SPEED_MIN || config->motor.base_speed > SPEED_MAX) {
        config->motor.base_speed = DEFAULT_BASE_SPEED;
        fixed = true;
    }
    
    if (config->motor.turn_speed_diff < TURN_DIFF_MIN || config->motor.turn_speed_diff > TURN_DIFF_MAX) {
        config->motor.turn_speed_diff = DEFAULT_TURN_DIFF;
        fixed = true;
    }
    
    if (config->motor.large_turn_speed < LARGE_TURN_MIN || config->motor.large_turn_speed > LARGE_TURN_MAX) {
        config->motor.large_turn_speed = DEFAULT_LARGE_TURN;
        fixed = true;
    }
    
    // 修复路径参数
    if (config->path.lost_line_threshold < LOST_THRESHOLD_MIN || config->path.lost_line_threshold > LOST_THRESHOLD_MAX) {
        config->path.lost_line_threshold = 10;
        fixed = true;
    }
    
    if (config->path.search_timeout < SEARCH_TIMEOUT_MIN || config->path.search_timeout > SEARCH_TIMEOUT_MAX) {
        config->path.search_timeout = 50;
        fixed = true;
    }
    
    // 同步电机B的PID参数
    config->pid.motor_b_kp = config->pid.motor_a_kp;
    config->pid.motor_b_ki = config->pid.motor_a_ki;
    config->pid.motor_b_kd = config->pid.motor_a_kd;
    
    if (fixed) {
        // 重新计算校验和
        config->checksum = config_calculate_checksum(config);
    }
    
    return true;
}

/**
 * @brief 导出配置到字符串
 */
int config_export_to_string(char* buffer, size_t buffer_size)
{
    if (!buffer || buffer_size == 0) return 0;
    
    system_config_t* config = get_system_config();
    
    return snprintf(buffer, buffer_size,
                   "CONFIG_EXPORT:PID_A(%.2f,%.2f,%.2f);PID_B(%.2f,%.2f,%.2f);MOTOR(%d,%d,%d);PATH(%d,%d)",
                   config->pid.motor_a_kp, config->pid.motor_a_ki, config->pid.motor_a_kd,
                   config->pid.motor_b_kp, config->pid.motor_b_ki, config->pid.motor_b_kd,
                   config->motor.base_speed, config->motor.turn_speed_diff, config->motor.large_turn_speed,
                   config->path.lost_line_threshold, config->path.search_timeout);
}
