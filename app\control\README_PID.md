# PID控制器使用说明

## 概述
本PID控制器实现了双电机的闭环速度控制，替换了原有的开环PWM控制方式。

## 主要特性
- 双电机独立PID控制
- 积分限幅防止积分饱和
- 输出限幅保护电机
- 10ms控制周期
- 可调PID参数

## 使用方法

### 1. 初始化
```c
// 在main函数中调用
motor_pid_init();
```

### 2. 设置目标速度
```c
// 设置目标速度(编码器增量/10ms)
motor_set_target_speed(50, 50);  // 两电机同速前进
motor_set_target_speed(50, -50); // 原地右转
motor_set_target_speed(0, 0);    // 停止
```

### 3. PID控制任务
```c
// 在10ms定时中断中调用
motor_pid_task();
```

## PID参数调优指南

### 当前默认参数
- Kp = 50.0  (比例系数)
- Ki = 0.5   (积分系数)
- Kd = 2.0   (微分系数)

### 调优步骤
1. **设置Ki=0, Kd=0，仅调Kp**
   - 从小值开始增加Kp
   - 直到系统有合适的响应速度但不振荡

2. **增加Ki**
   - 消除稳态误差
   - 如果出现振荡，减小Ki

3. **增加Kd**
   - 减少超调
   - 提高系统稳定性

### 参数修改方法
```c
// 运行时修改PID参数
pid_set_params(&motor_a_pid, new_kp, new_ki, new_kd);
pid_set_params(&motor_b_pid, new_kp, new_ki, new_kd);
```

## 速度单位说明
- 目标速度单位：编码器增量/10ms
- 典型值范围：-200 到 +200
- 正值：前进，负值：后退

## 注意事项
1. PID控制器需要在motor_pid_init()后才能正常工作
2. 确保编码器正常工作
3. PWM输出限制在±8000范围内
4. 建议在实际使用前进行参数调优

## 故障排除
- 如果电机不转：检查目标速度是否设置
- 如果振荡：减小Kp或Ki参数
- 如果响应慢：增加Kp参数
- 如果有稳态误差：增加Ki参数
