#ifndef _CONFIG_UTILS_H
#define _CONFIG_UTILS_H

#include "system_config.h"

/**
 * @brief 配置管理工具函数
 * @details 提供运行时配置调整和管理功能
 */

/**
 * @brief 打印当前配置信息
 * @param output_func 输出函数指针(如printf, uart_send等)
 */
void config_print_info(void (*output_func)(const char*));

/**
 * @brief 执行传感器校准
 * @param sensor_data 当前传感器原始数据
 * @param calibration_type 校准类型 (0:黑线, 1:白线)
 * @return true 成功, false 失败
 */
bool config_calibrate_sensor(const uint16_t* sensor_data, uint8_t calibration_type);

/**
 * @brief 自动校准传感器
 * @details 在黑线和白线上分别采集数据进行自动校准
 * @return true 成功, false 失败
 */
bool config_auto_calibrate_sensor(void);

/**
 * @brief 调整PID参数
 * @param motor_id 电机ID (0:A, 1:B, 2:Both)
 * @param param_type 参数类型 (0:Kp, 1:Ki, 2:Kd)
 * @param delta 调整量(正数增加，负数减少)
 * @return true 成功, false 失败
 */
bool config_adjust_pid_param(uint8_t motor_id, uint8_t param_type, float delta);

/**
 * @brief 调整电机速度参数
 * @param param_type 参数类型 (0:base_speed, 1:turn_diff, 2:large_turn)
 * @param delta 调整量
 * @return true 成功, false 失败
 */
bool config_adjust_motor_param(uint8_t param_type, int16_t delta);

/**
 * @brief 调整路径控制参数
 * @param param_type 参数类型 (0:lost_threshold, 1:search_timeout)
 * @param delta 调整量
 * @return true 成功, false 失败
 */
bool config_adjust_path_param(uint8_t param_type, int16_t delta);

/**
 * @brief 保存当前配置
 * @return true 成功, false 失败
 */
bool config_save_current(void);

/**
 * @brief 重置为默认配置
 * @return true 成功, false 失败
 */
bool config_reset_to_default(void);

/**
 * @brief 获取配置状态字符串
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 实际写入的字符数
 */
int config_get_status_string(char* buffer, size_t buffer_size);

/**
 * @brief 验证并修复配置
 * @details 检查配置参数是否在合理范围内，自动修复异常值
 * @return true 配置正常或已修复, false 配置严重错误
 */
bool config_validate_and_fix(void);

/**
 * @brief 导出配置到字符串
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 实际写入的字符数
 */
int config_export_to_string(char* buffer, size_t buffer_size);

/**
 * @brief 从字符串导入配置
 * @param config_string 配置字符串
 * @return true 成功, false 失败
 */
bool config_import_from_string(const char* config_string);

// 配置参数类型定义
typedef enum {
    CONFIG_PARAM_PID_KP = 0,
    CONFIG_PARAM_PID_KI,
    CONFIG_PARAM_PID_KD,
    CONFIG_PARAM_MOTOR_BASE_SPEED,
    CONFIG_PARAM_MOTOR_TURN_DIFF,
    CONFIG_PARAM_MOTOR_LARGE_TURN,
    CONFIG_PARAM_PATH_LOST_THRESHOLD,
    CONFIG_PARAM_PATH_SEARCH_TIMEOUT,
    CONFIG_PARAM_MAX
} config_param_type_t;

// 配置调整步长定义
#define PID_ADJUST_STEP         1.0f
#define SPEED_ADJUST_STEP       5
#define THRESHOLD_ADJUST_STEP   1

// 配置参数范围定义
#define PID_KP_MIN              0.0f
#define PID_KP_MAX              200.0f
#define PID_KI_MIN              0.0f
#define PID_KI_MAX              10.0f
#define PID_KD_MIN              0.0f
#define PID_KD_MAX              50.0f

#define SPEED_MIN               10
#define SPEED_MAX               300
#define TURN_DIFF_MIN           5
#define TURN_DIFF_MAX           100
#define LARGE_TURN_MIN          50
#define LARGE_TURN_MAX          400

#define LOST_THRESHOLD_MIN      1
#define LOST_THRESHOLD_MAX      100
#define SEARCH_TIMEOUT_MIN      10
#define SEARCH_TIMEOUT_MAX      200

#endif // _CONFIG_UTILS_H
