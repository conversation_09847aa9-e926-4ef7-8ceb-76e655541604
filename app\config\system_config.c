#include "system_config.h"
#include "string.h"

// 系统配置实例
static system_config_t g_system_config;

// 配置初始化标志
static bool config_initialized = false;

/**
 * @brief 恢复默认配置
 */
void config_restore_defaults(void)
{
    // 清零配置结构体
    memset(&g_system_config, 0, sizeof(system_config_t));
    
    // 设置魔数和版本
    g_system_config.magic = CONFIG_MAGIC;
    g_system_config.version = CONFIG_VERSION;
    
    // 传感器默认配置
    // 黑线校准值(来自原始硬编码)
    uint16_t default_black[SENSOR_COUNT] = {173, 1001, 1009, 869, 838, 869, 875, 914};
    // 白线校准值(来自原始硬编码)
    uint16_t default_white[SENSOR_COUNT] = {3106, 4066, 4091, 3714, 4055, 3387, 3811, 3971};
    
    memcpy(g_system_config.sensor.black_values, default_black, sizeof(default_black));
    memcpy(g_system_config.sensor.white_values, default_white, sizeof(default_white));
    
    // 计算默认阈值(黑白值的中间值)
    for (int i = 0; i < SENSOR_COUNT; i++) {
        g_system_config.sensor.threshold_values[i] = 
            (g_system_config.sensor.black_values[i] + g_system_config.sensor.white_values[i]) / 2;
    }
    
    g_system_config.sensor.filter_enable = 1;  // 使能滤波
    g_system_config.sensor.direction = 0;      // 默认方向
    
    // PID默认配置
    g_system_config.pid.motor_a_kp = DEFAULT_PID_KP;
    g_system_config.pid.motor_a_ki = DEFAULT_PID_KI;
    g_system_config.pid.motor_a_kd = DEFAULT_PID_KD;
    
    g_system_config.pid.motor_b_kp = DEFAULT_PID_KP;
    g_system_config.pid.motor_b_ki = DEFAULT_PID_KI;
    g_system_config.pid.motor_b_kd = DEFAULT_PID_KD;
    
    g_system_config.pid.output_max = 8000.0f;
    g_system_config.pid.output_min = -8000.0f;
    g_system_config.pid.integral_max = 6400.0f;  // 80% of output_max
    g_system_config.pid.integral_min = -6400.0f;
    g_system_config.pid.sample_time = 0.01f;     // 10ms
    
    // 电机默认配置
    g_system_config.motor.base_speed = DEFAULT_BASE_SPEED;
    g_system_config.motor.turn_speed_diff = DEFAULT_TURN_DIFF;
    g_system_config.motor.large_turn_speed = DEFAULT_LARGE_TURN;
    g_system_config.motor.max_speed = 200;
    g_system_config.motor.min_speed = 0;
    g_system_config.motor.motor_direction_a = 0;  // 正向
    g_system_config.motor.motor_direction_b = 0;  // 正向
    
    // 路径控制默认配置
    g_system_config.path.lost_line_threshold = 10;   // 100ms
    g_system_config.path.search_timeout = 50;        // 500ms
    g_system_config.path.search_direction = 0;       // 右转搜索
    g_system_config.path.auto_calibration = 0;       // 关闭自动校准
    
    // 显示默认配置
    g_system_config.display.oled_enable = 1;         // 使能OLED
    g_system_config.display.refresh_rate = 10;       // 10Hz
    g_system_config.display.brightness = 255;        // 最大亮度
    g_system_config.display.display_mode = 0;        // 默认显示模式
    
    // 调试默认配置
    g_system_config.debug.uart_enable = 0;           // 关闭串口调试
    g_system_config.debug.uart_baudrate = 115200;    // 波特率
    g_system_config.debug.log_level = 1;             // 基础日志级别
    g_system_config.debug.debug_mode = 0;            // 关闭调试模式
    
    // 计算校验和
    g_system_config.checksum = config_calculate_checksum(&g_system_config);
}

/**
 * @brief 计算配置校验和
 */
uint16_t config_calculate_checksum(const system_config_t* config)
{
    uint16_t checksum = 0;
    const uint8_t* data = (const uint8_t*)config;
    
    // 跳过checksum字段本身
    for (size_t i = 0; i < sizeof(system_config_t); i++) {
        if (i >= offsetof(system_config_t, checksum) && 
            i < offsetof(system_config_t, checksum) + sizeof(config->checksum)) {
            continue; // 跳过checksum字段
        }
        checksum += data[i];
    }
    
    return checksum;
}

/**
 * @brief 验证配置有效性
 */
bool config_validate(const system_config_t* config)
{
    // 检查魔数
    if (config->magic != CONFIG_MAGIC) {
        return false;
    }
    
    // 检查版本
    if (config->version != CONFIG_VERSION) {
        return false;
    }
    
    // 检查校验和
    uint16_t calculated_checksum = config_calculate_checksum(config);
    if (config->checksum != calculated_checksum) {
        return false;
    }
    
    // 检查关键参数范围
    if (config->motor.base_speed < 0 || config->motor.base_speed > 300) {
        return false;
    }
    
    if (config->pid.motor_a_kp < 0 || config->pid.motor_a_kp > 1000) {
        return false;
    }
    
    return true;
}

/**
 * @brief 系统配置初始化
 */
bool system_config_init(void)
{
    if (config_initialized) {
        return true;
    }
    
    // 尝试从Flash加载配置
    if (!config_load_from_flash()) {
        // 加载失败，使用默认配置
        config_restore_defaults();
    }
    
    config_initialized = true;
    return true;
}

/**
 * @brief 从Flash加载配置(简化实现)
 * 注意：实际实现需要根据具体芯片的Flash操作API
 */
bool config_load_from_flash(void)
{
    // TODO: 实现Flash读取
    // 这里使用默认配置作为示例
    config_restore_defaults();
    return true;
}

/**
 * @brief 保存配置到Flash(简化实现)
 * 注意：实际实现需要根据具体芯片的Flash操作API
 */
bool config_save_to_flash(void)
{
    // 更新校验和
    g_system_config.checksum = config_calculate_checksum(&g_system_config);
    
    // TODO: 实现Flash写入
    // 这里只是示例，实际需要调用芯片的Flash写入API
    
    return true;
}

/**
 * @brief 获取系统配置指针
 */
system_config_t* get_system_config(void)
{
    if (!config_initialized) {
        system_config_init();
    }
    return &g_system_config;
}

/**
 * @brief 获取传感器配置
 */
sensor_config_t* get_sensor_config(void)
{
    return &get_system_config()->sensor;
}

/**
 * @brief 获取PID配置
 */
pid_config_t* get_pid_config(void)
{
    return &get_system_config()->pid;
}

/**
 * @brief 获取电机配置
 */
motor_config_t* get_motor_config(void)
{
    return &get_system_config()->motor;
}

/**
 * @brief 获取路径配置
 */
path_config_t* get_path_config(void)
{
    return &get_system_config()->path;
}

/**
 * @brief 更新传感器校准值
 */
void config_update_sensor_calibration(const uint16_t* black_values, const uint16_t* white_values)
{
    sensor_config_t* sensor_cfg = get_sensor_config();
    
    if (black_values) {
        memcpy(sensor_cfg->black_values, black_values, sizeof(sensor_cfg->black_values));
    }
    
    if (white_values) {
        memcpy(sensor_cfg->white_values, white_values, sizeof(sensor_cfg->white_values));
    }
    
    // 重新计算阈值
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_cfg->threshold_values[i] = 
            (sensor_cfg->black_values[i] + sensor_cfg->white_values[i]) / 2;
    }
}

/**
 * @brief 更新PID参数
 */
void config_update_pid_params(uint8_t motor_id, float kp, float ki, float kd)
{
    pid_config_t* pid_cfg = get_pid_config();
    
    if (motor_id == 0) { // 电机A
        pid_cfg->motor_a_kp = kp;
        pid_cfg->motor_a_ki = ki;
        pid_cfg->motor_a_kd = kd;
    } else if (motor_id == 1) { // 电机B
        pid_cfg->motor_b_kp = kp;
        pid_cfg->motor_b_ki = ki;
        pid_cfg->motor_b_kd = kd;
    }
}

/**
 * @brief 更新电机参数
 */
void config_update_motor_params(int16_t base_speed, int16_t turn_diff, int16_t large_turn)
{
    motor_config_t* motor_cfg = get_motor_config();
    
    motor_cfg->base_speed = base_speed;
    motor_cfg->turn_speed_diff = turn_diff;
    motor_cfg->large_turn_speed = large_turn;
}
