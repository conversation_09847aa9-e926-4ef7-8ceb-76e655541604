#ifndef _GANWAY_H
#define _GANWAY_H

#include "stdint.h"

// 路径状态枚举
typedef enum {
    PATH_STATE_STRAIGHT = 0,    // 直行状态
    PATH_STATE_TURN_LEFT,       // 左转状态
    PATH_STATE_TURN_RIGHT,      // 右转状态
    PATH_STATE_SHARP_LEFT,      // 急左转状态
    PATH_STATE_SHARP_RIGHT,     // 急右转状态
    PATH_STATE_LOST,            // 丢线状态
    PATH_STATE_STOP,            // 停止状态
    PATH_STATE_SEARCH           // 搜索状态
} path_state_t;

// 路径事件枚举
typedef enum {
    PATH_EVENT_NONE = 0,        // 无事件
    PATH_EVENT_LINE_FOUND,      // 找到线
    PATH_EVENT_LINE_LOST,       // 丢线
    PATH_EVENT_TURN_COMPLETE,   // 转弯完成
    PATH_EVENT_SEARCH_TIMEOUT   // 搜索超时
} path_event_t;

// 路径决策表项
typedef struct {
    uint8_t sensor_pattern;     // 传感器模式(8位)
    path_state_t next_state;    // 下一状态
    int16_t speed_left;         // 左电机速度
    int16_t speed_right;        // 右电机速度
    const char* description;    // 状态描述
} path_decision_t;

// 路径控制器结构体
typedef struct {
    path_state_t current_state; // 当前状态
    path_state_t last_state;    // 上一状态
    uint32_t state_timer;       // 状态计时器
    uint8_t last_sensor_data;   // 上次传感器数据
    uint16_t lost_line_count;   // 丢线计数器
    uint16_t search_count;      // 搜索计数器
} path_controller_t;

// 基础速度定义(现在从配置管理获取)
#define BASE_SPEED          (get_motor_config()->base_speed)
#define TURN_SPEED_DIFF     (get_motor_config()->turn_speed_diff)
#define LARGE_TURN_SPEED    (get_motor_config()->large_turn_speed)
#define STOP_SPEED          0     // 停止速度

// 状态机参数(现在从配置管理获取)
#define LOST_LINE_THRESHOLD (get_path_config()->lost_line_threshold)
#define SEARCH_TIMEOUT      (get_path_config()->search_timeout)

/**
 * @brief 路径控制器初始化
 */
void path_controller_init(void);

/**
 * @brief 路径状态机处理
 * @param sensor_data 传感器数字量(8位)
 * @return path_event_t 路径事件
 */
path_event_t path_state_machine(uint8_t sensor_data);

/**
 * @brief 路径控制函数(兼容原接口)
 * @param x 传感器数字量(8位)
 */
void Way(unsigned char x);

/**
 * @brief 获取当前路径状态
 * @return path_state_t 当前状态
 */
path_state_t get_current_path_state(void);

/**
 * @brief 获取路径状态描述
 * @param state 路径状态
 * @return const char* 状态描述字符串
 */
const char* get_path_state_description(path_state_t state);

#endif