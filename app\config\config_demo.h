#ifndef _CONFIG_DEMO_H
#define _CONFIG_DEMO_H

/**
 * @brief 配置管理演示和测试函数
 * @details 提供配置管理系统的使用示例和测试功能
 */

/**
 * @brief 演示基本配置操作
 * @details 展示配置读取、修改、显示等基本功能
 */
void config_demo_basic_operations(void);

/**
 * @brief 演示传感器校准
 * @details 展示传感器黑白线校准过程
 */
void config_demo_sensor_calibration(void);

/**
 * @brief 演示配置验证和修复
 * @details 展示配置参数验证和自动修复功能
 */
void config_demo_validation(void);

/**
 * @brief 演示配置导出和导入
 * @details 展示配置的序列化和反序列化功能
 */
void config_demo_export_import(void);

/**
 * @brief 演示运行时参数调整
 * @details 展示运行时动态调整参数的功能
 */
void config_demo_runtime_adjustment(void);

/**
 * @brief 完整的配置管理演示
 * @details 执行所有演示功能的完整测试
 */
void config_demo_complete(void);

/**
 * @brief 配置管理性能测试
 * @details 测试配置访问和修改的性能
 */
void config_demo_performance_test(void);

#endif // _CONFIG_DEMO_H
