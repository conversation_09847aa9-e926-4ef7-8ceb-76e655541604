#include "config_demo.h"
#include "config_utils.h"
#include "stdio.h"

/**
 * @brief 配置管理演示函数
 * @details 演示如何使用配置管理系统
 */

// 模拟输出函数(可以替换为UART输出或OLED显示)
static void demo_output(const char* str)
{
    // 这里可以替换为实际的输出函数
    // 例如: uart_send_string(str); 或 OLED_ShowString(str);
    printf("%s", str);
}

/**
 * @brief 演示基本配置操作
 */
void config_demo_basic_operations(void)
{
    demo_output("=== 配置管理基本操作演示 ===\n");
    
    // 1. 显示当前配置
    demo_output("1. 当前配置信息:\n");
    config_print_info(demo_output);
    
    // 2. 调整PID参数
    demo_output("\n2. 调整PID参数 (Kp +5.0):\n");
    config_adjust_pid_param(2, CONFIG_PARAM_PID_KP, 5.0f); // 两个电机都调整
    config_print_info(demo_output);
    
    // 3. 调整电机速度
    demo_output("\n3. 调整基础速度 (+10):\n");
    config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, 10);
    config_print_info(demo_output);
    
    // 4. 获取状态字符串
    char status_buffer[256];
    config_get_status_string(status_buffer, sizeof(status_buffer));
    demo_output("\n4. 状态字符串:\n");
    demo_output(status_buffer);
    demo_output("\n");
}

/**
 * @brief 演示传感器校准
 */
void config_demo_sensor_calibration(void)
{
    demo_output("\n=== 传感器校准演示 ===\n");
    
    // 模拟传感器数据
    uint16_t black_data[8] = {200, 1100, 1200, 900, 850, 900, 950, 1000};
    uint16_t white_data[8] = {3200, 4200, 4300, 3800, 4100, 3500, 3900, 4100};
    
    demo_output("1. 校准前的传感器配置:\n");
    sensor_config_t* sensor_cfg = get_sensor_config();
    char buffer[256];
    snprintf(buffer, sizeof(buffer), "Black: %d,%d,%d,%d,%d,%d,%d,%d\n",
             sensor_cfg->black_values[0], sensor_cfg->black_values[1],
             sensor_cfg->black_values[2], sensor_cfg->black_values[3],
             sensor_cfg->black_values[4], sensor_cfg->black_values[5],
             sensor_cfg->black_values[6], sensor_cfg->black_values[7]);
    demo_output(buffer);
    
    // 执行校准
    demo_output("\n2. 执行黑线校准...\n");
    config_calibrate_sensor(black_data, 0);
    
    demo_output("3. 执行白线校准...\n");
    config_calibrate_sensor(white_data, 1);
    
    demo_output("\n4. 校准后的传感器配置:\n");
    snprintf(buffer, sizeof(buffer), "Black: %d,%d,%d,%d,%d,%d,%d,%d\n",
             sensor_cfg->black_values[0], sensor_cfg->black_values[1],
             sensor_cfg->black_values[2], sensor_cfg->black_values[3],
             sensor_cfg->black_values[4], sensor_cfg->black_values[5],
             sensor_cfg->black_values[6], sensor_cfg->black_values[7]);
    demo_output(buffer);
    
    snprintf(buffer, sizeof(buffer), "White: %d,%d,%d,%d,%d,%d,%d,%d\n",
             sensor_cfg->white_values[0], sensor_cfg->white_values[1],
             sensor_cfg->white_values[2], sensor_cfg->white_values[3],
             sensor_cfg->white_values[4], sensor_cfg->white_values[5],
             sensor_cfg->white_values[6], sensor_cfg->white_values[7]);
    demo_output(buffer);
}

/**
 * @brief 演示配置验证和修复
 */
void config_demo_validation(void)
{
    demo_output("\n=== 配置验证和修复演示 ===\n");
    
    // 1. 故意设置错误的配置值
    demo_output("1. 设置异常配置值...\n");
    pid_config_t* pid_cfg = get_pid_config();
    motor_config_t* motor_cfg = get_motor_config();
    
    pid_cfg->motor_a_kp = 999.0f;  // 超出范围
    motor_cfg->base_speed = -50;   // 负值
    
    demo_output("2. 验证前的配置:\n");
    config_print_info(demo_output);
    
    // 2. 执行验证和修复
    demo_output("\n3. 执行配置验证和修复...\n");
    bool result = config_validate_and_fix();
    
    char buffer[128];
    snprintf(buffer, sizeof(buffer), "验证结果: %s\n", result ? "成功" : "失败");
    demo_output(buffer);
    
    demo_output("\n4. 修复后的配置:\n");
    config_print_info(demo_output);
}

/**
 * @brief 演示配置导出和导入
 */
void config_demo_export_import(void)
{
    demo_output("\n=== 配置导出和导入演示 ===\n");
    
    // 1. 导出配置
    char export_buffer[512];
    int len = config_export_to_string(export_buffer, sizeof(export_buffer));
    
    demo_output("1. 导出的配置字符串:\n");
    demo_output(export_buffer);
    demo_output("\n");
    
    char buffer[128];
    snprintf(buffer, sizeof(buffer), "导出长度: %d 字节\n", len);
    demo_output(buffer);
}

/**
 * @brief 演示运行时参数调整
 */
void config_demo_runtime_adjustment(void)
{
    demo_output("\n=== 运行时参数调整演示 ===\n");
    
    demo_output("1. 初始配置:\n");
    config_print_info(demo_output);
    
    // 模拟按键调整参数
    demo_output("\n2. 模拟按键调整 (增加Kp):\n");
    config_adjust_pid_param(2, CONFIG_PARAM_PID_KP, PID_ADJUST_STEP);
    config_print_info(demo_output);
    
    demo_output("\n3. 模拟按键调整 (减少基础速度):\n");
    config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, -SPEED_ADJUST_STEP);
    config_print_info(demo_output);
    
    demo_output("\n4. 模拟按键调整 (增加丢线阈值):\n");
    config_adjust_path_param(CONFIG_PARAM_PATH_LOST_THRESHOLD, THRESHOLD_ADJUST_STEP);
    config_print_info(demo_output);
}

/**
 * @brief 完整的配置管理演示
 */
void config_demo_complete(void)
{
    demo_output("========================================\n");
    demo_output("       配置管理系统完整演示\n");
    demo_output("========================================\n");
    
    // 执行各种演示
    config_demo_basic_operations();
    config_demo_sensor_calibration();
    config_demo_validation();
    config_demo_export_import();
    config_demo_runtime_adjustment();
    
    demo_output("\n========================================\n");
    demo_output("           演示完成\n");
    demo_output("========================================\n");
}

/**
 * @brief 配置管理性能测试
 */
void config_demo_performance_test(void)
{
    demo_output("\n=== 配置管理性能测试 ===\n");
    
    // 测试配置访问速度
    uint32_t start_time = 0; // 这里需要替换为实际的时间获取函数
    
    // 执行1000次配置访问
    for (int i = 0; i < 1000; i++) {
        motor_config_t* motor_cfg = get_motor_config();
        volatile int16_t speed = motor_cfg->base_speed; // 防止编译器优化
        (void)speed; // 避免未使用变量警告
    }
    
    uint32_t end_time = 0; // 这里需要替换为实际的时间获取函数
    
    char buffer[128];
    snprintf(buffer, sizeof(buffer), "1000次配置访问耗时: %lu ms\n", end_time - start_time);
    demo_output(buffer);
    
    // 测试配置修改速度
    start_time = 0;
    
    for (int i = 0; i < 100; i++) {
        config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, 1);
        config_adjust_motor_param(CONFIG_PARAM_MOTOR_BASE_SPEED, -1);
    }
    
    end_time = 0;
    
    snprintf(buffer, sizeof(buffer), "100次配置修改耗时: %lu ms\n", end_time - start_time);
    demo_output(buffer);
}
